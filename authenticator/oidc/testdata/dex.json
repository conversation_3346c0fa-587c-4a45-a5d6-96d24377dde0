{"issuer": "https://argocd.example.com/api/dex", "authorization_endpoint": "https://argocd.example.com/api/dex/auth", "token_endpoint": "https://argocd.example.com/api/dex/token", "jwks_uri": "https://argocd.example.com/api/dex/keys", "response_types_supported": ["code"], "subject_types_supported": ["public"], "id_token_signing_alg_values_supported": ["RS256"], "scopes_supported": ["openid", "email", "groups", "profile", "offline_access"], "token_endpoint_auth_methods_supported": ["client_secret_basic"], "claims_supported": ["aud", "email", "email_verified", "exp", "iat", "iss", "locale", "name", "sub"]}