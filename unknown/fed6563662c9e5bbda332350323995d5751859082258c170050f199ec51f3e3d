# SourceCloudRepoSourceContext

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**RepoId** | [***SourceRepoId**](sourceRepoId.md) | The ID of the repo. | [optional] [default to null]
**RevisionId** | **string** | A revision ID. | [optional] [default to null]
**AliasContext** | [***SourceAliasContext**](sourceAliasContext.md) | An alias, which may be a branch or tag. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


