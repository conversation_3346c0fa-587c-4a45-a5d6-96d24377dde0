# VulnerabilityPackageIssue

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AffectedLocation** | [***VulnerabilityVulnerabilityLocation**](vulnerabilityVulnerabilityLocation.md) | Required. The location of the vulnerability. | [optional] [default to null]
**FixedLocation** | [***VulnerabilityVulnerabilityLocation**](vulnerabilityVulnerabilityLocation.md) | The location of the available fix for vulnerability. | [optional] [default to null]
**SeverityName** | **string** | Deprecated, use Details.effective_severity instead The severity (e.g., distro assigned severity) for this vulnerability. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


