# VulnerabilityDetail

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CpeUri** | **string** | Required. The CPE URI in [cpe format](https://cpe.mitre.org/specification/) in which the vulnerability manifests. Examples include distro or storage location for vulnerable jar. | [optional] [default to null]
**Package_** | **string** | Required. The name of the package where the vulnerability was found. | [optional] [default to null]
**MinAffectedVersion** | [***PackageVersion**](packageVersion.md) | The min version of the package in which the vulnerability exists. | [optional] [default to null]
**MaxAffectedVersion** | [***PackageVersion**](packageVersion.md) | The max version of the package in which the vulnerability exists. | [optional] [default to null]
**SeverityName** | **string** | The severity (eg: distro assigned severity) for this vulnerability. | [optional] [default to null]
**Description** | **string** | A vendor-specific description of this note. | [optional] [default to null]
**FixedLocation** | [***VulnerabilityVulnerabilityLocation**](vulnerabilityVulnerabilityLocation.md) | The fix for this specific package version. | [optional] [default to null]
**PackageType** | **string** | The type of package; whether native or non native(ruby gems, node.js packages etc). | [optional] [default to null]
**IsObsolete** | **bool** | Whether this detail is obsolete. Occurrences are expected not to point to obsolete details. | [optional] [default to null]
**SourceUpdateTime** | [**time.Time**](time.Time.md) | The time this information was last changed at the source. This is an upstream timestamp from the underlying information source - e.g. Ubuntu security tracker. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


