[{"Category": "ARGOCD_INFORMER", "Fields": [{"Env": "ACD_INFORMER", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "Used to determine whether ArgoCD informer is enabled or not", "Example": "", "Deprecated": "false"}, {"Env": "ACD_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "Namespace where all the ArgoCD application objects are published. For multi-cluster mode, it will be set to v1.NamespaceAll", "Example": "", "Deprecated": "false"}]}, {"Category": "CD_ARGO_WORKFLOW", "Fields": [{"Env": "CD_DEFAULT_NAMESPACE", "EnvType": "string", "EnvValue": "devtron-cd", "EnvDescription": "Namespace where all CD workflows objects are scheduled. For multi-cluster mode, it will be set to v1.NamespaceAll", "Example": "", "Deprecated": "false"}, {"Env": "CD_INFORMER", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "Used to determine whether CD informer is enabled or not", "Example": "", "Deprecated": "false"}]}, {"Category": "CI_ARGO_WORKFLOW", "Fields": [{"Env": "CI_INFORMER", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "Used to determine whether CI informer is enabled or not", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_NAMESPACE", "EnvType": "string", "EnvValue": "devtron-ci", "EnvDescription": "Namespace where all CI workflows objects are scheduled. For multi-cluster mode, it will be set to v1.NamespaceAll", "Example": "", "Deprecated": "false"}]}, {"Category": "CLUSTER_MODE", "Fields": [{"Env": "CLUSTER_ARGO_CD_TYPE", "EnvType": "string", "EnvValue": "IN_CLUSTER", "EnvDescription": "Determines cluster mode for ArgoCD informer; for multiple cluster mode, it will be set to ALL_CLUSTER; for single cluster mode, it will be set to IN_CLUSTER", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CD_ARGO_WF_TYPE", "EnvType": "string", "EnvValue": "IN_CLUSTER", "EnvDescription": "Determines cluster mode for CD ArgoWorkflow informer; for multiple cluster mode, it will be set to ALL_CLUSTER; for single cluster mode, it will be set to IN_CLUSTER", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CI_ARGO_WF_TYPE", "EnvType": "string", "EnvValue": "IN_CLUSTER", "EnvDescription": "Determines cluster mode for CI ArgoWorkflow informer; for multiple cluster mode, it will be set to ALL_CLUSTER; for single cluster mode, it will be set to IN_CLUSTER", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_TYPE", "EnvType": "string", "EnvValue": "IN_CLUSTER", "EnvDescription": "Determines cluster mode for System Executor informer; for multiple cluster mode, it will be set to ALL_CLUSTER; for single cluster mode, it will be set to IN_CLUSTER", "Example": "", "Deprecated": "false"}]}, {"Category": "DEVTRON", "Fields": [{"Env": "APP", "EnvType": "string", "EnvValue": "kubewatch", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CONSUMER_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_LOG_TIME_LIMIT", "EnvType": "int64", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_STATSVIZ", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST", "EnvType": "int", "EnvValue": "25", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_IDLE_CONN_TIMEOUT", "EnvType": "int", "EnvValue": "300", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_KEEPALIVE", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_TIMEOUT", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TLS_HANDSHAKE_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LOG_LEVEL", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_ACK_WAIT_IN_SECS", "EnvType": "int", "EnvValue": "120", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_BUFFER_SIZE", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_MAX_AGE", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_PROCESSING_BATCH_SIZE", "EnvType": "int", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_REPLICAS", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_SERVER_HOST", "EnvType": "string", "EnvValue": "nats://devtron-nats.devtroncd:4222", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_ADDR", "EnvType": "string", "EnvValue": "127.0.0.1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_DATABASE", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_EXPORT_PROM_METRICS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_FAILURE_QUERIES", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_QUERY", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_SLOW_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PASSWORD", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PORT", "EnvType": "string", "EnvValue": "5432", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_QUERY_DUR_THRESHOLD", "EnvType": "int64", "EnvValue": "5000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_USER", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "RUNTIME_CONFIG_LOCAL_DEV", "EnvType": "LocalDevMode", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "STREAM_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USE_CUSTOM_HTTP_TRANSPORT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}, {"Category": "EXTERNAL_KUBEWATCH", "Fields": [{"Env": "CD_EXTERNAL_LISTENER_URL", "EnvType": "string", "EnvValue": "http://devtroncd-orchestrator-service-prod.devtroncd:80", "EnvDescription": "URL of the orchestrator", "Example": "", "Deprecated": "false"}, {"Env": "CD_EXTERNAL_NAMESPACE", "EnvType": "string", "EnvValue": "", "EnvDescription": "Namespace where the external kubewatch is set up", "Example": "", "Deprecated": "false"}, {"Env": "CD_EXTERNAL_ORCHESTRATOR_TOKEN", "EnvType": "string", "EnvValue": "", "EnvDescription": "<PERSON><PERSON> used to authenticate with the orchestrator", "Example": "", "Deprecated": "false"}, {"Env": "CD_EXTERNAL_REST_LISTENER", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "Used to determine whether it's an external kubewatch or internal kubewatch", "Example": "", "Deprecated": "false"}]}, {"Category": "GRACEFUL_SHUTDOWN", "Fields": [{"Env": "SLEEP_TIMEOUT", "EnvType": "int", "EnvValue": "5", "EnvDescription": "Graceful shutdown timeout in seconds", "Example": "", "Deprecated": "false"}]}]