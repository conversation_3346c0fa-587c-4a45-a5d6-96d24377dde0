# AttestationAttestation

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**PgpSignedAttestation** | [***AttestationPgpSignedAttestation**](attestationPgpSignedAttestation.md) | A PGP signed attestation. | [optional] [default to null]
**GenericSignedAttestation** | [***AttestationGenericSignedAttestation**](attestationGenericSignedAttestation.md) | An attestation that supports multiple &#x60;Signature&#x60;s over the same attestation payload. The signatures (defined in common.proto) support a superset of public key types and IDs compared to PgpSignedAttestation. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


