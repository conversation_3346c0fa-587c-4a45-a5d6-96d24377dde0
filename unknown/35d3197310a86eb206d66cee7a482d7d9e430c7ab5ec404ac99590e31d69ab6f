/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

--- drop column is_active in ci_pipeline_material_webhook_data_mapping table
alter table ci_pipeline_material_webhook_data_mapping
drop column is_active;


--- drop column payload_data_id in webhook_event_parsed_data table
alter table webhook_event_parsed_data
drop column payload_data_id;


--- drop column created_on in ci_pipeline_material_webhook_data_mapping table
alter table ci_pipeline_material_webhook_data_mapping
drop column created_on;


--- drop column updated_on in ci_pipeline_material_webhook_data_mapping table
alter table ci_pipeline_material_webhook_data_mapping
drop column updated_on;


---- drop table ci_pipeline_material_webhook_data_mapping_filter_result
DROP TABLE IF EXISTS public.ci_pipeline_material_webhook_data_mapping_filter_result;

---- DROP sequence
DROP SEQUENCE IF EXISTS public.ci_pipeline_material_webhook_data_mapping_filter_result_id_seq;