# ImageBasis

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ResourceUrl** | **string** | Required. Immutable. The resource_url for the resource representing the basis of associated occurrence images. | [optional] [default to null]
**Fingerprint** | [***ImageFingerprint**](imageFingerprint.md) | Required. Immutable. The fingerprint of the base image. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


