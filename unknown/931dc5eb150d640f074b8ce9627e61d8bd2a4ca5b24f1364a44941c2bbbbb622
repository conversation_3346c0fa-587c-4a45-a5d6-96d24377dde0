# SourceSourceContext

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CloudRepo** | [***SourceCloudRepoSourceContext**](sourceCloudRepoSourceContext.md) | A SourceContext referring to a revision in a Google Cloud Source Repo. | [optional] [default to null]
**Gerrit** | [***SourceGerritSourceContext**](sourceGerritSourceContext.md) | A SourceContext referring to a Gerrit project. | [optional] [default to null]
**Git** | [***SourceGitSourceContext**](sourceGitSourceContext.md) | A SourceContext referring to any third party Git repo (e.g., GitHub). | [optional] [default to null]
**Labels** | **map[string]string** | Labels with user defined metadata. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


