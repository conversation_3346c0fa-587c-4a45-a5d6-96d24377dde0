# V1beta1BatchCreateOccurrencesRequest

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Parent** | **string** | The name of the project in the form of &#x60;projects/[PROJECT_ID]&#x60;, under which the occurrences are to be created. | [optional] [default to null]
**Occurrences** | [**[]V1beta1Occurrence**](v1beta1Occurrence.md) | The occurrences to create. Max allowed length is 1000. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


