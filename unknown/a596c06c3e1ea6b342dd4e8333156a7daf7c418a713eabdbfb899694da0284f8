# Go API client for grafeas

No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)

## Overview
This API client was generated by the [swagger-codegen](https://github.com/swagger-api/swagger-codegen) project.  By using the [swagger-spec](https://github.com/swagger-api/swagger-spec) from a remote server, you can easily generate an API client.

- API version: version not set
- Package version: 0.1.4
- Build package: io.swagger.codegen.languages.GoClientCodegen

## Installation
Put the package under your project folder and add the following in import:
```golang
import "./grafeas"
```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*GrafeasV1Beta1Api* | [**BatchCreateNotes**](docs/GrafeasV1Beta1Api.md#batchcreatenotes) | **Post** /v1beta1/{parent&#x3D;projects/*}/notes:batchCreate | Creates new notes in batch.
*GrafeasV1Beta1Api* | [**BatchCreateOccurrences**](docs/GrafeasV1Beta1Api.md#batchcreateoccurrences) | **Post** /v1beta1/{parent&#x3D;projects/*}/occurrences:batchCreate | Creates new occurrences in batch.
*GrafeasV1Beta1Api* | [**CreateNote**](docs/GrafeasV1Beta1Api.md#createnote) | **Post** /v1beta1/{parent&#x3D;projects/*}/notes | Creates a new note.
*GrafeasV1Beta1Api* | [**CreateOccurrence**](docs/GrafeasV1Beta1Api.md#createoccurrence) | **Post** /v1beta1/{parent&#x3D;projects/*}/occurrences | Creates a new occurrence.
*GrafeasV1Beta1Api* | [**DeleteNote**](docs/GrafeasV1Beta1Api.md#deletenote) | **Delete** /v1beta1/{name&#x3D;projects/*/notes/*} | Deletes the specified note.
*GrafeasV1Beta1Api* | [**DeleteOccurrence**](docs/GrafeasV1Beta1Api.md#deleteoccurrence) | **Delete** /v1beta1/{name&#x3D;projects/*/occurrences/*} | Deletes the specified occurrence. For example, use this method to delete an occurrence when the occurrence is no longer applicable for the given resource.
*GrafeasV1Beta1Api* | [**GetNote**](docs/GrafeasV1Beta1Api.md#getnote) | **Get** /v1beta1/{name&#x3D;projects/*/notes/*} | Gets the specified note.
*GrafeasV1Beta1Api* | [**GetOccurrence**](docs/GrafeasV1Beta1Api.md#getoccurrence) | **Get** /v1beta1/{name&#x3D;projects/*/occurrences/*} | Gets the specified occurrence.
*GrafeasV1Beta1Api* | [**GetOccurrenceNote**](docs/GrafeasV1Beta1Api.md#getoccurrencenote) | **Get** /v1beta1/{name&#x3D;projects/*/occurrences/*}/notes | Gets the note attached to the specified occurrence. Consumer projects can use this method to get a note that belongs to a provider project.
*GrafeasV1Beta1Api* | [**GetVulnerabilityOccurrencesSummary**](docs/GrafeasV1Beta1Api.md#getvulnerabilityoccurrencessummary) | **Get** /v1beta1/{parent&#x3D;projects/*}/occurrences:vulnerabilitySummary | Gets a summary of the number and severity of occurrences.
*GrafeasV1Beta1Api* | [**ListNoteOccurrences**](docs/GrafeasV1Beta1Api.md#listnoteoccurrences) | **Get** /v1beta1/{name&#x3D;projects/*/notes/*}/occurrences | Lists occurrences referencing the specified note. Provider projects can use this method to get all occurrences across consumer projects referencing the specified note.
*GrafeasV1Beta1Api* | [**ListNotes**](docs/GrafeasV1Beta1Api.md#listnotes) | **Get** /v1beta1/{parent&#x3D;projects/*}/notes | Lists notes for the specified project.
*GrafeasV1Beta1Api* | [**ListOccurrences**](docs/GrafeasV1Beta1Api.md#listoccurrences) | **Get** /v1beta1/{parent&#x3D;projects/*}/occurrences | Lists occurrences for the specified project.
*GrafeasV1Beta1Api* | [**UpdateNote**](docs/GrafeasV1Beta1Api.md#updatenote) | **Patch** /v1beta1/{name&#x3D;projects/*/notes/*} | Updates the specified note.
*GrafeasV1Beta1Api* | [**UpdateOccurrence**](docs/GrafeasV1Beta1Api.md#updateoccurrence) | **Patch** /v1beta1/{name&#x3D;projects/*/occurrences/*} | Updates the specified occurrence.


## Documentation For Models

 - [AliasContextKind](docs/AliasContextKind.md)
 - [AttestationAttestation](docs/AttestationAttestation.md)
 - [AttestationAuthority](docs/AttestationAuthority.md)
 - [AttestationGenericSignedAttestation](docs/AttestationGenericSignedAttestation.md)
 - [AttestationGenericSignedAttestationContentType](docs/AttestationGenericSignedAttestationContentType.md)
 - [AttestationPgpSignedAttestation](docs/AttestationPgpSignedAttestation.md)
 - [AttestationPgpSignedAttestationContentType](docs/AttestationPgpSignedAttestationContentType.md)
 - [AuthorityHint](docs/AuthorityHint.md)
 - [BuildBuild](docs/BuildBuild.md)
 - [BuildBuildSignature](docs/BuildBuildSignature.md)
 - [BuildSignatureKeyType](docs/BuildSignatureKeyType.md)
 - [CvsSv3AttackComplexity](docs/CvsSv3AttackComplexity.md)
 - [CvsSv3AttackVector](docs/CvsSv3AttackVector.md)
 - [CvsSv3Impact](docs/CvsSv3Impact.md)
 - [CvsSv3PrivilegesRequired](docs/CvsSv3PrivilegesRequired.md)
 - [CvsSv3Scope](docs/CvsSv3Scope.md)
 - [CvsSv3UserInteraction](docs/CvsSv3UserInteraction.md)
 - [DeploymentDeployable](docs/DeploymentDeployable.md)
 - [DeploymentDeployment](docs/DeploymentDeployment.md)
 - [DeploymentPlatform](docs/DeploymentPlatform.md)
 - [DiscoveredAnalysisStatus](docs/DiscoveredAnalysisStatus.md)
 - [DiscoveredContinuousAnalysis](docs/DiscoveredContinuousAnalysis.md)
 - [DiscoveryDiscovered](docs/DiscoveryDiscovered.md)
 - [DiscoveryDiscovery](docs/DiscoveryDiscovery.md)
 - [Grafeasv1beta1Signature](docs/Grafeasv1beta1Signature.md)
 - [HashHashType](docs/HashHashType.md)
 - [ImageBasis](docs/ImageBasis.md)
 - [ImageDerived](docs/ImageDerived.md)
 - [ImageFingerprint](docs/ImageFingerprint.md)
 - [ImageLayer](docs/ImageLayer.md)
 - [InTotoArtifactRule](docs/InTotoArtifactRule.md)
 - [IntotoInToto](docs/IntotoInToto.md)
 - [IntotoLink](docs/IntotoLink.md)
 - [IntotoLinkArtifact](docs/IntotoLinkArtifact.md)
 - [IntotoSigningKey](docs/IntotoSigningKey.md)
 - [LayerDirective](docs/LayerDirective.md)
 - [LinkArtifactHashes](docs/LinkArtifactHashes.md)
 - [LinkByProducts](docs/LinkByProducts.md)
 - [LinkEnvironment](docs/LinkEnvironment.md)
 - [PackageArchitecture](docs/PackageArchitecture.md)
 - [PackageDistribution](docs/PackageDistribution.md)
 - [PackageInstallation](docs/PackageInstallation.md)
 - [PackagePackage](docs/PackagePackage.md)
 - [PackageVersion](docs/PackageVersion.md)
 - [ProtobufAny](docs/ProtobufAny.md)
 - [ProtobufFieldMask](docs/ProtobufFieldMask.md)
 - [ProvenanceBuildProvenance](docs/ProvenanceBuildProvenance.md)
 - [ProvenanceCommand](docs/ProvenanceCommand.md)
 - [ProvenanceFileHashes](docs/ProvenanceFileHashes.md)
 - [ProvenanceHash](docs/ProvenanceHash.md)
 - [ProvenanceSource](docs/ProvenanceSource.md)
 - [RpcStatus](docs/RpcStatus.md)
 - [SourceAliasContext](docs/SourceAliasContext.md)
 - [SourceCloudRepoSourceContext](docs/SourceCloudRepoSourceContext.md)
 - [SourceGerritSourceContext](docs/SourceGerritSourceContext.md)
 - [SourceGitSourceContext](docs/SourceGitSourceContext.md)
 - [SourceProjectRepoId](docs/SourceProjectRepoId.md)
 - [SourceRepoId](docs/SourceRepoId.md)
 - [SourceSourceContext](docs/SourceSourceContext.md)
 - [V1beta1BatchCreateNotesRequest](docs/V1beta1BatchCreateNotesRequest.md)
 - [V1beta1BatchCreateNotesResponse](docs/V1beta1BatchCreateNotesResponse.md)
 - [V1beta1BatchCreateOccurrencesRequest](docs/V1beta1BatchCreateOccurrencesRequest.md)
 - [V1beta1BatchCreateOccurrencesResponse](docs/V1beta1BatchCreateOccurrencesResponse.md)
 - [V1beta1ListNoteOccurrencesResponse](docs/V1beta1ListNoteOccurrencesResponse.md)
 - [V1beta1ListNotesResponse](docs/V1beta1ListNotesResponse.md)
 - [V1beta1ListOccurrencesResponse](docs/V1beta1ListOccurrencesResponse.md)
 - [V1beta1Note](docs/V1beta1Note.md)
 - [V1beta1NoteKind](docs/V1beta1NoteKind.md)
 - [V1beta1Occurrence](docs/V1beta1Occurrence.md)
 - [V1beta1RelatedUrl](docs/V1beta1RelatedUrl.md)
 - [V1beta1Resource](docs/V1beta1Resource.md)
 - [V1beta1VulnerabilityOccurrencesSummary](docs/V1beta1VulnerabilityOccurrencesSummary.md)
 - [V1beta1attestationDetails](docs/V1beta1attestationDetails.md)
 - [V1beta1buildDetails](docs/V1beta1buildDetails.md)
 - [V1beta1deploymentDetails](docs/V1beta1deploymentDetails.md)
 - [V1beta1discoveryDetails](docs/V1beta1discoveryDetails.md)
 - [V1beta1imageDetails](docs/V1beta1imageDetails.md)
 - [V1beta1intotoDetails](docs/V1beta1intotoDetails.md)
 - [V1beta1intotoSignature](docs/V1beta1intotoSignature.md)
 - [V1beta1packageDetails](docs/V1beta1packageDetails.md)
 - [V1beta1packageLocation](docs/V1beta1packageLocation.md)
 - [V1beta1provenanceArtifact](docs/V1beta1provenanceArtifact.md)
 - [V1beta1vulnerabilityDetails](docs/V1beta1vulnerabilityDetails.md)
 - [VersionVersionKind](docs/VersionVersionKind.md)
 - [VulnerabilityCvsSv3](docs/VulnerabilityCvsSv3.md)
 - [VulnerabilityDetail](docs/VulnerabilityDetail.md)
 - [VulnerabilityOccurrencesSummaryFixableTotalByDigest](docs/VulnerabilityOccurrencesSummaryFixableTotalByDigest.md)
 - [VulnerabilityPackageIssue](docs/VulnerabilityPackageIssue.md)
 - [VulnerabilitySeverity](docs/VulnerabilitySeverity.md)
 - [VulnerabilityVulnerability](docs/VulnerabilityVulnerability.md)
 - [VulnerabilityVulnerabilityLocation](docs/VulnerabilityVulnerabilityLocation.md)
 - [VulnerabilityWindowsDetail](docs/VulnerabilityWindowsDetail.md)
 - [WindowsDetailKnowledgeBase](docs/WindowsDetailKnowledgeBase.md)


## Documentation For Authorization
 Endpoints do not require authorization.


## Author



