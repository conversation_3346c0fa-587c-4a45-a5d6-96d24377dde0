version: 0.0.1
cdPipelineConf:
  - beforeStages:
      - name: "test-1"
        script: |
          date > test.report
          echo 'hello'
        outputLocation: "./test.report"
      - name: "test-2"
        script: |
          date > test2.report
        outputLocation: "./test2.report"
  - afterStages:
      - name: "test-1"
        script: |
          date > test.report
          echo 'hello'
        outputLocation: "./test.report"
      - name: "test-2"
        script: |
          date > test2.report
        outputLocation: "./test2.report"