[{"Category": "DEVTRON", "Fields": [{"Env": "DEVTRON_DEFAULT_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_SECRET_NAME", "EnvType": "string", "EnvValue": "devtron-secret", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_CLIENT_ID", "EnvType": "string", "EnvValue": "argo-cd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_HOST", "EnvType": "string", "EnvValue": "http://localhost", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_PORT", "EnvType": "string", "EnvValue": "5556", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "RUNTIME_CONFIG_LOCAL_DEV", "EnvType": "LocalDevMode", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USER_SESSION_DURATION_SECONDS", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}]