# SourceRepoId

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ProjectRepoId** | [***SourceProjectRepoId**](sourceProjectRepoId.md) | A combination of a project ID and a repo name. | [optional] [default to null]
**Uid** | **string** | A server-assigned, globally unique identifier. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


