# PackageInstallation

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Name** | **string** | Output only. The name of the installed package. | [optional] [default to null]
**Location** | [**[]V1beta1packageLocation**](v1beta1packageLocation.md) | Required. All of the places within the filesystem versions of this package have been found. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


