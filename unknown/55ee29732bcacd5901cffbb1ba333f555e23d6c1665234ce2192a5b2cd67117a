# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/
.DS_Store
.idea
.vscode
.env
/cmd/external-app/devtron-ea

#binaries
authenticator/authenticator
chart-sync/chart-sync
ci-runner/cirunner
git-sensor/git-sensor
kubelink/kubelink
kubewatch/kubewatch
lens/lens
image-scanner/image-scanner