# PackagePackage

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Name** | **string** | Required. Immutable. The name of the package. | [optional] [default to null]
**Distribution** | [**[]PackageDistribution**](packageDistribution.md) | The various channels by which a package is distributed. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


