# VulnerabilityCvsSv3

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**BaseScore** | **float32** | The base score is a function of the base metric scores. | [optional] [default to null]
**ExploitabilityScore** | **float32** |  | [optional] [default to null]
**ImpactScore** | **float32** |  | [optional] [default to null]
**AttackVector** | [***CvsSv3AttackVector**](CVSSv3AttackVector.md) | Base Metrics Represents the intrinsic characteristics of a vulnerability that are constant over time and across user environments. | [optional] [default to null]
**AttackComplexity** | [***CvsSv3AttackComplexity**](CVSSv3AttackComplexity.md) |  | [optional] [default to null]
**PrivilegesRequired** | [***CvsSv3PrivilegesRequired**](CVSSv3PrivilegesRequired.md) |  | [optional] [default to null]
**UserInteraction** | [***CvsSv3UserInteraction**](CVSSv3UserInteraction.md) |  | [optional] [default to null]
**Scope** | [***CvsSv3Scope**](CVSSv3Scope.md) |  | [optional] [default to null]
**ConfidentialityImpact** | [***CvsSv3Impact**](CVSSv3Impact.md) |  | [optional] [default to null]
**IntegrityImpact** | [***CvsSv3Impact**](CVSSv3Impact.md) |  | [optional] [default to null]
**AvailabilityImpact** | [***CvsSv3Impact**](CVSSv3Impact.md) |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


