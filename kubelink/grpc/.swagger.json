{"swagger": "2.0", "info": {"title": "", "version": "version not set"}, "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"protobufAny": {"type": "object", "properties": {"type_url": {"type": "string"}, "value": {"type": "string", "format": "byte"}}}, "runtimeError": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}