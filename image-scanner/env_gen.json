[{"Category": "DEVTRON", "Fields": [{"Env": "APP", "EnvType": "string", "EnvValue": "image-scanner", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLAIR_ADDR", "EnvType": "string", "EnvValue": "http://localhost:6060", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLAIR_TIMEOUT", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CONSUMER_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_LOG_TIME_LIMIT", "EnvType": "int64", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_PROGRESSING_SCAN_CHECK", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_STATSVIZ", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_SCAN_ASYNC_TIMEOUT", "EnvType": "int", "EnvValue": "3", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_SCAN_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "IMAGE_SCAN_TRY_COUNT", "EnvType": "int", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "JSON_OUTPUT", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LOG_LEVEL", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_ACK_WAIT_IN_SECS", "EnvType": "int", "EnvValue": "120", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_BUFFER_SIZE", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_MAX_AGE", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_PROCESSING_BATCH_SIZE", "EnvType": "int", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_REPLICAS", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_SERVER_HOST", "EnvType": "string", "EnvValue": "nats://devtron-nats.devtroncd:4222", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_ADDR", "EnvType": "string", "EnvValue": "127.0.0.1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_DATABASE", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_EXPORT_PROM_METRICS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_FAILURE_QUERIES", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_QUERY", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_SLOW_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PASSWORD", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PORT", "EnvType": "string", "EnvValue": "5432", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_QUERY_DUR_THRESHOLD", "EnvType": "int64", "EnvValue": "5000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_USER", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PROJECT_ID", "EnvType": "string", "EnvValue": "projects/devtron-project-id", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "SCANNER_TYPE", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "SERVER_HTTP_PORT", "EnvType": "int", "EnvValue": "8080", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "STREAM_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}]