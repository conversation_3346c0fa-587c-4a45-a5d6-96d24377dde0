---
swagger: "2.0"
info:
  version: "version not set"
  title: "grafeas.proto"
schemes:
- "http"
- "https"
consumes:
- "application/json"
produces:
- "application/json"
paths:
  /v1beta1/{name=projects/*/notes/*}:
    get:
      tags:
      - "GrafeasV1Beta1"
      summary: "Gets the specified note."
      operationId: "GetNote"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1Note"
    delete:
      tags:
      - "GrafeasV1Beta1"
      summary: "Deletes the specified note."
      operationId: "DeleteNote"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      responses:
        200:
          description: "A successful response."
          schema: {}
    patch:
      tags:
      - "GrafeasV1Beta1"
      summary: "Updates the specified note."
      operationId: "UpdateNote"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      - in: "body"
        name: "body"
        description: "The updated note."
        required: true
        schema:
          $ref: "#/definitions/v1beta1Note"
        x-exportParamName: "Body"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1Note"
  /v1beta1/{name=projects/*/notes/*}/occurrences:
    get:
      tags:
      - "GrafeasV1Beta1"
      summary: "Lists occurrences referencing the specified note. Provider projects\
        \ can use\nthis method to get all occurrences across consumer projects referencing\
        \ the\nspecified note."
      operationId: "ListNoteOccurrences"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the note to list occurrences for in the form of\n\
          `projects/[PROVIDER_ID]/notes/[NOTE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      - name: "filter"
        in: "query"
        description: "The filter expression."
        required: false
        type: "string"
        x-exportParamName: "Filter"
        x-optionalDataType: "String"
      - name: "page_size"
        in: "query"
        description: "Number of occurrences to return in the list."
        required: false
        type: "integer"
        format: "int32"
        x-exportParamName: "PageSize"
        x-optionalDataType: "Int32"
      - name: "page_token"
        in: "query"
        description: "Token to provide to skip to a particular spot in the list."
        required: false
        type: "string"
        x-exportParamName: "PageToken"
        x-optionalDataType: "String"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1ListNoteOccurrencesResponse"
  /v1beta1/{name=projects/*/occurrences/*}:
    get:
      tags:
      - "GrafeasV1Beta1"
      summary: "Gets the specified occurrence."
      operationId: "GetOccurrence"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1Occurrence"
    delete:
      tags:
      - "GrafeasV1Beta1"
      summary: "Deletes the specified occurrence. For example, use this method to\
        \ delete an\noccurrence when the occurrence is no longer applicable for the\
        \ given\nresource."
      operationId: "DeleteOccurrence"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      responses:
        200:
          description: "A successful response."
          schema: {}
    patch:
      tags:
      - "GrafeasV1Beta1"
      summary: "Updates the specified occurrence."
      operationId: "UpdateOccurrence"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      - in: "body"
        name: "body"
        description: "The updated occurrence."
        required: true
        schema:
          $ref: "#/definitions/v1beta1Occurrence"
        x-exportParamName: "Body"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1Occurrence"
  /v1beta1/{name=projects/*/occurrences/*}/notes:
    get:
      tags:
      - "GrafeasV1Beta1"
      summary: "Gets the note attached to the specified occurrence. Consumer projects\
        \ can\nuse this method to get a note that belongs to a provider project."
      operationId: "GetOccurrenceNote"
      parameters:
      - name: "name"
        in: "path"
        description: "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Name"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1Note"
  /v1beta1/{parent=projects/*}/notes:
    get:
      tags:
      - "GrafeasV1Beta1"
      summary: "Lists notes for the specified project."
      operationId: "ListNotes"
      parameters:
      - name: "parent"
        in: "path"
        description: "The name of the project to list notes for in the form of\n`projects/[PROJECT_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Parent"
      - name: "filter"
        in: "query"
        description: "The filter expression."
        required: false
        type: "string"
        x-exportParamName: "Filter"
        x-optionalDataType: "String"
      - name: "page_size"
        in: "query"
        description: "Number of notes to return in the list. Must be positive. Max\
          \ allowed page\nsize is 1000. If not specified, page size defaults to 20."
        required: false
        type: "integer"
        format: "int32"
        x-exportParamName: "PageSize"
        x-optionalDataType: "Int32"
      - name: "page_token"
        in: "query"
        description: "Token to provide to skip to a particular spot in the list."
        required: false
        type: "string"
        x-exportParamName: "PageToken"
        x-optionalDataType: "String"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1ListNotesResponse"
    post:
      tags:
      - "GrafeasV1Beta1"
      summary: "Creates a new note."
      operationId: "CreateNote"
      parameters:
      - name: "parent"
        in: "path"
        description: "The name of the project in the form of `projects/[PROJECT_ID]`,\
          \ under which\nthe note is to be created."
        required: true
        type: "string"
        x-exportParamName: "Parent"
      - in: "body"
        name: "body"
        description: "The note to create."
        required: true
        schema:
          $ref: "#/definitions/v1beta1Note"
        x-exportParamName: "Body"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1Note"
  /v1beta1/{parent=projects/*}/notes:batchCreate:
    post:
      tags:
      - "GrafeasV1Beta1"
      summary: "Creates new notes in batch."
      operationId: "BatchCreateNotes"
      parameters:
      - name: "parent"
        in: "path"
        description: "The name of the project in the form of `projects/[PROJECT_ID]`,\
          \ under which\nthe notes are to be created."
        required: true
        type: "string"
        x-exportParamName: "Parent"
      - in: "body"
        name: "body"
        required: true
        schema:
          $ref: "#/definitions/v1beta1BatchCreateNotesRequest"
        x-exportParamName: "Body"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1BatchCreateNotesResponse"
  /v1beta1/{parent=projects/*}/occurrences:
    get:
      tags:
      - "GrafeasV1Beta1"
      summary: "Lists occurrences for the specified project."
      operationId: "ListOccurrences"
      parameters:
      - name: "parent"
        in: "path"
        description: "The name of the project to list occurrences for in the form\
          \ of\n`projects/[PROJECT_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Parent"
      - name: "filter"
        in: "query"
        description: "The filter expression."
        required: false
        type: "string"
        x-exportParamName: "Filter"
        x-optionalDataType: "String"
      - name: "page_size"
        in: "query"
        description: "Number of occurrences to return in the list. Must be positive.\
          \ Max allowed\npage size is 1000. If not specified, page size defaults to\
          \ 20."
        required: false
        type: "integer"
        format: "int32"
        x-exportParamName: "PageSize"
        x-optionalDataType: "Int32"
      - name: "page_token"
        in: "query"
        description: "Token to provide to skip to a particular spot in the list."
        required: false
        type: "string"
        x-exportParamName: "PageToken"
        x-optionalDataType: "String"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1ListOccurrencesResponse"
    post:
      tags:
      - "GrafeasV1Beta1"
      summary: "Creates a new occurrence."
      operationId: "CreateOccurrence"
      parameters:
      - name: "parent"
        in: "path"
        description: "The name of the project in the form of `projects/[PROJECT_ID]`,\
          \ under which\nthe occurrence is to be created."
        required: true
        type: "string"
        x-exportParamName: "Parent"
      - in: "body"
        name: "body"
        description: "The occurrence to create."
        required: true
        schema:
          $ref: "#/definitions/v1beta1Occurrence"
        x-exportParamName: "Body"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1Occurrence"
  /v1beta1/{parent=projects/*}/occurrences:batchCreate:
    post:
      tags:
      - "GrafeasV1Beta1"
      summary: "Creates new occurrences in batch."
      operationId: "BatchCreateOccurrences"
      parameters:
      - name: "parent"
        in: "path"
        description: "The name of the project in the form of `projects/[PROJECT_ID]`,\
          \ under which\nthe occurrences are to be created."
        required: true
        type: "string"
        x-exportParamName: "Parent"
      - in: "body"
        name: "body"
        required: true
        schema:
          $ref: "#/definitions/v1beta1BatchCreateOccurrencesRequest"
        x-exportParamName: "Body"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1BatchCreateOccurrencesResponse"
  /v1beta1/{parent=projects/*}/occurrences:vulnerabilitySummary:
    get:
      tags:
      - "GrafeasV1Beta1"
      summary: "Gets a summary of the number and severity of occurrences."
      operationId: "GetVulnerabilityOccurrencesSummary"
      parameters:
      - name: "parent"
        in: "path"
        description: "The name of the project to get a vulnerability summary for in\
          \ the form of\n`projects/[PROJECT_ID]`."
        required: true
        type: "string"
        x-exportParamName: "Parent"
      - name: "filter"
        in: "query"
        description: "The filter expression."
        required: false
        type: "string"
        x-exportParamName: "Filter"
        x-optionalDataType: "String"
      responses:
        200:
          description: "A successful response."
          schema:
            $ref: "#/definitions/v1beta1VulnerabilityOccurrencesSummary"
definitions:
  AliasContextKind:
    type: "string"
    description: "The type of an alias.\n\n - KIND_UNSPECIFIED: Unknown.\n - FIXED:\
      \ Git tag.\n - MOVABLE: Git branch.\n - OTHER: Used to specify non-standard\
      \ aliases. For example, if a Git repo has a\nref named \"refs/foo/bar\"."
    enum:
    - "KIND_UNSPECIFIED"
    - "FIXED"
    - "MOVABLE"
    - "OTHER"
    default: "KIND_UNSPECIFIED"
  AuthorityHint:
    type: "object"
    properties:
      human_readable_name:
        type: "string"
        description: "Required. The human readable name of this attestation authority,\
          \ for\nexample \"qa\"."
    description: "This submessage provides human-readable hints about the purpose\
      \ of the\nauthority. Because the name of a note acts as its resource reference,\
      \ it is\nimportant to disambiguate the canonical name of the Note (which might\
      \ be a\nUUID for security purposes) from \"readable\" names more suitable for\
      \ debug\noutput. Note that these hints should not be used to look up authorities\
      \ in\nsecurity sensitive contexts, such as when looking up attestations to\n\
      verify."
    example:
      human_readable_name: "human_readable_name"
  BuildSignatureKeyType:
    type: "string"
    description: "Public key formats.\n\n - KEY_TYPE_UNSPECIFIED: `KeyType` is not\
      \ set.\n - PGP_ASCII_ARMORED: `PGP ASCII Armored` public key.\n - PKIX_PEM:\
      \ `PKIX PEM` public key."
    enum:
    - "KEY_TYPE_UNSPECIFIED"
    - "PGP_ASCII_ARMORED"
    - "PKIX_PEM"
    default: "KEY_TYPE_UNSPECIFIED"
  CVSSv3AttackComplexity:
    type: "string"
    enum:
    - "ATTACK_COMPLEXITY_UNSPECIFIED"
    - "ATTACK_COMPLEXITY_LOW"
    - "ATTACK_COMPLEXITY_HIGH"
    default: "ATTACK_COMPLEXITY_UNSPECIFIED"
  CVSSv3AttackVector:
    type: "string"
    enum:
    - "ATTACK_VECTOR_UNSPECIFIED"
    - "ATTACK_VECTOR_NETWORK"
    - "ATTACK_VECTOR_ADJACENT"
    - "ATTACK_VECTOR_LOCAL"
    - "ATTACK_VECTOR_PHYSICAL"
    default: "ATTACK_VECTOR_UNSPECIFIED"
  CVSSv3Impact:
    type: "string"
    enum:
    - "IMPACT_UNSPECIFIED"
    - "IMPACT_HIGH"
    - "IMPACT_LOW"
    - "IMPACT_NONE"
    default: "IMPACT_UNSPECIFIED"
  CVSSv3PrivilegesRequired:
    type: "string"
    enum:
    - "PRIVILEGES_REQUIRED_UNSPECIFIED"
    - "PRIVILEGES_REQUIRED_NONE"
    - "PRIVILEGES_REQUIRED_LOW"
    - "PRIVILEGES_REQUIRED_HIGH"
    default: "PRIVILEGES_REQUIRED_UNSPECIFIED"
  CVSSv3Scope:
    type: "string"
    enum:
    - "SCOPE_UNSPECIFIED"
    - "SCOPE_UNCHANGED"
    - "SCOPE_CHANGED"
    default: "SCOPE_UNSPECIFIED"
  CVSSv3UserInteraction:
    type: "string"
    enum:
    - "USER_INTERACTION_UNSPECIFIED"
    - "USER_INTERACTION_NONE"
    - "USER_INTERACTION_REQUIRED"
    default: "USER_INTERACTION_UNSPECIFIED"
  DeploymentPlatform:
    type: "string"
    description: "Types of platforms.\n\n - PLATFORM_UNSPECIFIED: Unknown.\n - GKE:\
      \ Google Container Engine.\n - FLEX: Google App Engine: Flexible Environment.\n\
      \ - CUSTOM: Custom user-defined platform."
    enum:
    - "PLATFORM_UNSPECIFIED"
    - "GKE"
    - "FLEX"
    - "CUSTOM"
    default: "PLATFORM_UNSPECIFIED"
  DiscoveredAnalysisStatus:
    type: "string"
    description: "Analysis status for a resource. Currently for initial analysis only\
      \ (not\nupdated in continuous analysis).\n\n - ANALYSIS_STATUS_UNSPECIFIED:\
      \ Unknown.\n - PENDING: Resource is known but no action has been taken yet.\n\
      \ - SCANNING: Resource is being analyzed.\n - FINISHED_SUCCESS: Analysis has\
      \ finished successfully.\n - FINISHED_FAILED: Analysis has finished unsuccessfully,\
      \ the analysis itself is in a bad\nstate.\n - FINISHED_UNSUPPORTED: The resource\
      \ is known not to be supported"
    enum:
    - "ANALYSIS_STATUS_UNSPECIFIED"
    - "PENDING"
    - "SCANNING"
    - "FINISHED_SUCCESS"
    - "FINISHED_FAILED"
    - "FINISHED_UNSUPPORTED"
    default: "ANALYSIS_STATUS_UNSPECIFIED"
  DiscoveredContinuousAnalysis:
    type: "string"
    description: "Whether the resource is continuously analyzed.\n\n - CONTINUOUS_ANALYSIS_UNSPECIFIED:\
      \ Unknown.\n - ACTIVE: The resource is continuously analyzed.\n - INACTIVE:\
      \ The resource is ignored for continuous analysis."
    enum:
    - "CONTINUOUS_ANALYSIS_UNSPECIFIED"
    - "ACTIVE"
    - "INACTIVE"
    default: "CONTINUOUS_ANALYSIS_UNSPECIFIED"
  HashHashType:
    type: "string"
    description: "Specifies the hash algorithm.\n\n - HASH_TYPE_UNSPECIFIED: Unknown.\n\
      \ - SHA256: A SHA-256 hash."
    enum:
    - "HASH_TYPE_UNSPECIFIED"
    - "SHA256"
    default: "HASH_TYPE_UNSPECIFIED"
  InTotoArtifactRule:
    type: "object"
    properties:
      artifact_rule:
        type: "array"
        items:
          type: "string"
    title: "Defines an object to declare an in-toto artifact rule"
    example:
      artifact_rule:
      - "artifact_rule"
      - "artifact_rule"
  LayerDirective:
    type: "string"
    description: "Instructions from Dockerfile.\n\n - DIRECTIVE_UNSPECIFIED: Default\
      \ value for unsupported/missing directive.\n - MAINTAINER: https://docs.docker.com/engine/reference/builder/\n\
      \ - RUN: https://docs.docker.com/engine/reference/builder/\n - CMD: https://docs.docker.com/engine/reference/builder/\n\
      \ - LABEL: https://docs.docker.com/engine/reference/builder/\n - EXPOSE: https://docs.docker.com/engine/reference/builder/\n\
      \ - ENV: https://docs.docker.com/engine/reference/builder/\n - ADD: https://docs.docker.com/engine/reference/builder/\n\
      \ - COPY: https://docs.docker.com/engine/reference/builder/\n - ENTRYPOINT:\
      \ https://docs.docker.com/engine/reference/builder/\n - VOLUME: https://docs.docker.com/engine/reference/builder/\n\
      \ - USER: https://docs.docker.com/engine/reference/builder/\n - WORKDIR: https://docs.docker.com/engine/reference/builder/\n\
      \ - ARG: https://docs.docker.com/engine/reference/builder/\n - ONBUILD: https://docs.docker.com/engine/reference/builder/\n\
      \ - STOPSIGNAL: https://docs.docker.com/engine/reference/builder/\n - HEALTHCHECK:\
      \ https://docs.docker.com/engine/reference/builder/\n - SHELL: https://docs.docker.com/engine/reference/builder/"
    enum:
    - "DIRECTIVE_UNSPECIFIED"
    - "MAINTAINER"
    - "RUN"
    - "CMD"
    - "LABEL"
    - "EXPOSE"
    - "ENV"
    - "ADD"
    - "COPY"
    - "ENTRYPOINT"
    - "VOLUME"
    - "USER"
    - "WORKDIR"
    - "ARG"
    - "ONBUILD"
    - "STOPSIGNAL"
    - "HEALTHCHECK"
    - "SHELL"
    default: "DIRECTIVE_UNSPECIFIED"
  LinkArtifactHashes:
    type: "object"
    properties:
      sha256:
        type: "string"
    description: "Defines a hash object for use in Materials and Products."
    example:
      sha256: "sha256"
  LinkByProducts:
    type: "object"
    properties:
      custom_values:
        type: "object"
        additionalProperties:
          type: "string"
    description: "Defines an object for the byproducts field in in-toto links. The\
      \ suggested\nfields are \"stderr\", \"stdout\", and \"return-value\"."
    example:
      custom_values:
        key: "custom_values"
  LinkEnvironment:
    type: "object"
    properties:
      custom_values:
        type: "object"
        additionalProperties:
          type: "string"
    description: "Defines an object for the environment field in in-toto links. The\
      \ suggested\nfields are \"variables\", \"filesystem\", and \"workdir\"."
    example:
      custom_values:
        key: "custom_values"
  VersionVersionKind:
    type: "string"
    description: "Whether this is an ordinary package version or a sentinel MIN/MAX\
      \ version.\n\n - VERSION_KIND_UNSPECIFIED: Unknown.\n - NORMAL: A standard package\
      \ version.\n - MINIMUM: A special version representing negative infinity.\n\
      \ - MAXIMUM: A special version representing positive infinity."
    enum:
    - "VERSION_KIND_UNSPECIFIED"
    - "NORMAL"
    - "MINIMUM"
    - "MAXIMUM"
    default: "VERSION_KIND_UNSPECIFIED"
  VulnerabilityDetail:
    type: "object"
    properties:
      cpe_uri:
        type: "string"
        description: "Required. The CPE URI in\n[cpe format](https://cpe.mitre.org/specification/)\
          \ in which the\nvulnerability manifests. Examples include distro or storage\
          \ location for\nvulnerable jar."
      package:
        type: "string"
        description: "Required. The name of the package where the vulnerability was\
          \ found."
      min_affected_version:
        description: "The min version of the package in which the vulnerability exists."
        $ref: "#/definitions/packageVersion"
      max_affected_version:
        description: "The max version of the package in which the vulnerability exists."
        $ref: "#/definitions/packageVersion"
      severity_name:
        type: "string"
        description: "The severity (eg: distro assigned severity) for this vulnerability."
      description:
        type: "string"
        description: "A vendor-specific description of this note."
      fixed_location:
        description: "The fix for this specific package version."
        $ref: "#/definitions/vulnerabilityVulnerabilityLocation"
      package_type:
        type: "string"
        description: "The type of package; whether native or non native(ruby gems,\
          \ node.js\npackages etc)."
      is_obsolete:
        type: "boolean"
        format: "boolean"
        description: "Whether this detail is obsolete. Occurrences are expected not\
          \ to point to\nobsolete details."
      source_update_time:
        type: "string"
        format: "date-time"
        description: "The time this information was last changed at the source. This\
          \ is an\nupstream timestamp from the underlying information source - e.g.\
          \ Ubuntu\nsecurity tracker."
    title: "Identifies all appearances of this vulnerability in the package for a\n\
      specific distro/location. For example: glibc in\ncpe:/o:debian:debian_linux:8\
      \ for versions 2.1 - 2.2"
    example:
      fixed_location:
        package: "package"
        cpe_uri: "cpe_uri"
        version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
      package: "package"
      cpe_uri: "cpe_uri"
      max_affected_version:
        kind: {}
        name: "name"
        epoch: 6
        revision: "revision"
      description: "description"
      min_affected_version:
        kind: {}
        name: "name"
        epoch: 6
        revision: "revision"
      severity_name: "severity_name"
      source_update_time: "2000-01-23T04:56:07.000+00:00"
      package_type: "package_type"
      is_obsolete: true
  VulnerabilityOccurrencesSummaryFixableTotalByDigest:
    type: "object"
    properties:
      resource:
        description: "The affected resource."
        $ref: "#/definitions/v1beta1Resource"
      severity:
        description: "The severity for this count. SEVERITY_UNSPECIFIED indicates\
          \ total across\nall severities."
        $ref: "#/definitions/vulnerabilitySeverity"
      fixable_count:
        type: "string"
        format: "int64"
        description: "The number of fixable vulnerabilities associated with this resource."
      total_count:
        type: "string"
        format: "int64"
        description: "The total number of vulnerabilities associated with this resource."
    description: "Per resource and severity counts of fixable and total vulnerabilities."
    example:
      severity: {}
      resource:
        name: "name"
        content_hash:
          type: {}
          value: "value"
        uri: "uri"
      total_count: "total_count"
      fixable_count: "fixable_count"
  VulnerabilityWindowsDetail:
    type: "object"
    properties:
      cpe_uri:
        type: "string"
        description: "Required. The CPE URI in\n[cpe format](https://cpe.mitre.org/specification/)\
          \ in which the\nvulnerability manifests. Examples include distro or storage\
          \ location for\nvulnerable jar."
      name:
        type: "string"
        description: "Required. The name of the vulnerability."
      description:
        type: "string"
        description: "The description of the vulnerability."
      fixing_kbs:
        type: "array"
        description: "Required. The names of the KBs which have hotfixes to mitigate\
          \ this\nvulnerability. Note that there may be multiple hotfixes (and thus\n\
          multiple KBs) that mitigate a given vulnerability. Currently any listed\n\
          kb's presence is considered a fix."
        items:
          $ref: "#/definitions/WindowsDetailKnowledgeBase"
    example:
      cpe_uri: "cpe_uri"
      fixing_kbs:
      - name: "name"
        url: "url"
      - name: "name"
        url: "url"
      name: "name"
      description: "description"
  WindowsDetailKnowledgeBase:
    type: "object"
    properties:
      name:
        type: "string"
        description: "The KB name (generally of the form KB[0-9]+ i.e. KB123456)."
      url:
        type: "string"
        title: "A link to the KB in the Windows update catalog -\nhttps://www.catalog.update.microsoft.com/"
    example:
      name: "name"
      url: "url"
  attestationAttestation:
    type: "object"
    properties:
      pgp_signed_attestation:
        description: "A PGP signed attestation."
        $ref: "#/definitions/attestationPgpSignedAttestation"
      generic_signed_attestation:
        description: "An attestation that supports multiple `Signature`s\nover the\
          \ same attestation payload. The signatures\n(defined in common.proto) support\
          \ a superset of\npublic key types and IDs compared to PgpSignedAttestation."
        $ref: "#/definitions/attestationGenericSignedAttestation"
    description: "Occurrence that represents a single \"attestation\". The authenticity\
      \ of an\nattestation can be verified using the attached signature. If the verifier\n\
      trusts the public key of the signer, then verifying the signature is\nsufficient\
      \ to establish trust. In this circumstance, the authority to which\nthis attestation\
      \ is attached is primarily useful for look-up (how to find\nthis attestation\
      \ if you already know the authority and artifact to be\nverified) and intent\
      \ (which authority was this attestation intended to sign\nfor)."
    example:
      pgp_signed_attestation:
        content_type: {}
        signature: "signature"
        pgp_key_id: "pgp_key_id"
      generic_signed_attestation:
        content_type: {}
        serialized_payload: "serialized_payload"
        signatures:
        - signature: "signature"
          public_key_id: "public_key_id"
        - signature: "signature"
          public_key_id: "public_key_id"
  attestationAuthority:
    type: "object"
    properties:
      hint:
        description: "Hint hints at the purpose of the attestation authority."
        $ref: "#/definitions/AuthorityHint"
    description: "Note kind that represents a logical attestation \"role\" or \"authority\"\
      . For\nexample, an organization might have one `Authority` for \"QA\" and one\
      \ for\n\"build\". This note is intended to act strictly as a grouping mechanism\
      \ for\nthe attached occurrences (Attestations). This grouping mechanism also\n\
      provides a security boundary, since IAM ACLs gate the ability for a principle\n\
      to attach an occurrence to a given note. It also provides a single point of\n\
      lookup to find all attached attestation occurrences, even if they don't all\n\
      live in the same project."
    example:
      hint:
        human_readable_name: "human_readable_name"
  attestationGenericSignedAttestation:
    type: "object"
    properties:
      content_type:
        description: "Type (for example schema) of the attestation payload that was\
          \ signed.\nThe verifier must ensure that the provided type is one that the\
          \ verifier\nsupports, and that the attestation payload is a valid instantiation\
          \ of that\ntype (for example by validating a JSON schema)."
        $ref: "#/definitions/attestationGenericSignedAttestationContentType"
      serialized_payload:
        type: "string"
        format: "byte"
        description: "The serialized payload that is verified by one or more `signatures`.\n\
          The encoding and semantic meaning of this payload must match what is set\
          \ in\n`content_type`."
        pattern: "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"
      signatures:
        type: "array"
        description: "One or more signatures over `serialized_payload`.  Verifier\
          \ implementations\nshould consider this attestation message verified if\
          \ at least one\n`signature` verifies `serialized_payload`.  See `Signature`\
          \ in common.proto\nfor more details on signature structure and verification."
        items:
          $ref: "#/definitions/grafeasv1beta1Signature"
    description: "An attestation wrapper that uses the Grafeas `Signature` message.\n\
      This attestation must define the `serialized_payload` that the `signatures`\
      \ verify\nand any metadata necessary to interpret that plaintext.  The signatures\n\
      should always be over the `serialized_payload` bytestring."
    example:
      content_type: {}
      serialized_payload: "serialized_payload"
      signatures:
      - signature: "signature"
        public_key_id: "public_key_id"
      - signature: "signature"
        public_key_id: "public_key_id"
  attestationGenericSignedAttestationContentType:
    type: "string"
    description: "Type of the attestation plaintext that was signed.\n\n - CONTENT_TYPE_UNSPECIFIED:\
      \ `ContentType` is not set.\n - SIMPLE_SIGNING_JSON: Atomic format attestation\
      \ signature. See\nhttps://github.com/containers/image/blob/8a5d2f82a6e3263290c8e0276c3e0f64e77723e7/docs/atomic-signature.md\n\
      The payload extracted in `plaintext` is a JSON blob conforming to the\nlinked\
      \ schema."
    enum:
    - "CONTENT_TYPE_UNSPECIFIED"
    - "SIMPLE_SIGNING_JSON"
    default: "CONTENT_TYPE_UNSPECIFIED"
  attestationPgpSignedAttestation:
    type: "object"
    properties:
      signature:
        type: "string"
        description: "Required. The raw content of the signature, as output by GNU\
          \ Privacy Guard\n(GPG) or equivalent. Since this message only supports attached\
          \ signatures,\nthe payload that was signed must be attached. While the signature\
          \ format\nsupported is dependent on the verification implementation, currently\
          \ only\nASCII-armored (`--armor` to gpg), non-clearsigned (`--sign` rather\
          \ than\n`--clearsign` to gpg) are supported. Concretely, `gpg --sign --armor\n\
          --output=signature.gpg payload.json` will create the signature content\n\
          expected in this field in `signature.gpg` for the `payload.json`\nattestation\
          \ payload."
      content_type:
        description: "Type (for example schema) of the attestation payload that was\
          \ signed.\nThe verifier must ensure that the provided type is one that the\
          \ verifier\nsupports, and that the attestation payload is a valid instantiation\
          \ of that\ntype (for example by validating a JSON schema)."
        $ref: "#/definitions/attestationPgpSignedAttestationContentType"
      pgp_key_id:
        type: "string"
        description: "The cryptographic fingerprint of the key used to generate the\
          \ signature,\nas output by, e.g. `gpg --list-keys`. This should be the version\
          \ 4, full\n160-bit fingerprint, expressed as a 40 character hexidecimal\
          \ string. See\nhttps://tools.ietf.org/html/rfc4880#section-12.2 for details.\n\
          Implementations may choose to acknowledge \"LONG\", \"SHORT\", or other\n\
          abbreviated key IDs, but only the full fingerprint is guaranteed to work.\n\
          In gpg, the full fingerprint can be retrieved from the `fpr` field\nreturned\
          \ when calling --list-keys with --with-colons.  For example:\n```\ngpg --with-colons\
          \ --with-fingerprint --force-v4-certs \\\n    --list-keys <EMAIL>\n\
          tru::1:1513631572:0:3:1:5\npub:...<SNIP>...\nfpr:::::::::24FF6481B76AC91E66A00AC657A93A81EF3AE6FB:\n\
          ```\nAbove, the fingerprint is `24FF6481B76AC91E66A00AC657A93A81EF3AE6FB`."
    description: "An attestation wrapper with a PGP-compatible signature. This message\
      \ only\nsupports `ATTACHED` signatures, where the payload that is signed is\
      \ included\nalongside the signature itself in the same file."
    example:
      content_type: {}
      signature: "signature"
      pgp_key_id: "pgp_key_id"
  attestationPgpSignedAttestationContentType:
    type: "string"
    description: "Type (for example schema) of the attestation payload that was signed.\n\
      \n - CONTENT_TYPE_UNSPECIFIED: `ContentType` is not set.\n - SIMPLE_SIGNING_JSON:\
      \ Atomic format attestation signature. See\nhttps://github.com/containers/image/blob/8a5d2f82a6e3263290c8e0276c3e0f64e77723e7/docs/atomic-signature.md\n\
      The payload extracted from `signature` is a JSON blob conforming to the\nlinked\
      \ schema."
    enum:
    - "CONTENT_TYPE_UNSPECIFIED"
    - "SIMPLE_SIGNING_JSON"
    default: "CONTENT_TYPE_UNSPECIFIED"
  buildBuild:
    type: "object"
    properties:
      builder_version:
        type: "string"
        description: "Required. Immutable. Version of the builder which produced this\
          \ build."
      signature:
        description: "Signature of the build in occurrences pointing to this build\
          \ note\ncontaining build details."
        $ref: "#/definitions/buildBuildSignature"
    description: "Note holding the version of the provider's builder and the signature\
      \ of the\nprovenance message in the build details occurrence."
    example:
      signature:
        public_key: "public_key"
        key_type: {}
        signature: "signature"
        key_id: "key_id"
      builder_version: "builder_version"
  buildBuildSignature:
    type: "object"
    properties:
      public_key:
        type: "string"
        description: "Public key of the builder which can be used to verify that the\
          \ related\nfindings are valid and unchanged. If `key_type` is empty, this\
          \ defaults\nto PEM encoded public keys.\n\nThis field may be empty if `key_id`\
          \ references an external key.\n\nFor Cloud Build based signatures, this\
          \ is a PEM encoded public\nkey. To verify the Cloud Build signature, place\
          \ the contents of\nthis field into a file (public.pem). The signature field\
          \ is base64-decoded\ninto its binary representation in signature.bin, and\
          \ the provenance bytes\nfrom `BuildDetails` are base64-decoded into a binary\
          \ representation in\nsigned.bin. OpenSSL can then verify the signature:\n\
          `openssl sha256 -verify public.pem -signature signature.bin signed.bin`"
      signature:
        type: "string"
        format: "byte"
        description: "Required. Signature of the related `BuildProvenance`. In JSON,\
          \ this is\nbase-64 encoded."
        pattern: "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"
      key_id:
        type: "string"
        description: "An ID for the key used to sign. This could be either an ID for\
          \ the key\nstored in `public_key` (such as the ID or fingerprint for a PGP\
          \ key, or the\nCN for a cert), or a reference to an external key (such as\
          \ a reference to a\nkey in Cloud Key Management Service)."
      key_type:
        description: "The type of the key, either stored in `public_key` or referenced\
          \ in\n`key_id`."
        $ref: "#/definitions/BuildSignatureKeyType"
    description: "Message encapsulating the signature of the verified build."
    example:
      public_key: "public_key"
      key_type: {}
      signature: "signature"
      key_id: "key_id"
  deploymentDeployable:
    type: "object"
    properties:
      resource_uri:
        type: "array"
        description: "Required. Resource URI for the artifact being deployed."
        items:
          type: "string"
    description: "An artifact that can be deployed in some runtime."
    example:
      resource_uri:
      - "resource_uri"
      - "resource_uri"
  deploymentDeployment:
    type: "object"
    properties:
      user_email:
        type: "string"
        description: "Identity of the user that triggered this deployment."
      deploy_time:
        type: "string"
        format: "date-time"
        description: "Required. Beginning of the lifetime of this deployment."
      undeploy_time:
        type: "string"
        format: "date-time"
        description: "End of the lifetime of this deployment."
      config:
        type: "string"
        description: "Configuration used to create this deployment."
      address:
        type: "string"
        description: "Address of the runtime element hosting this deployment."
      resource_uri:
        type: "array"
        description: "Output only. Resource URI for the artifact being deployed taken\
          \ from\nthe deployable field with the same name."
        readOnly: true
        items:
          type: "string"
      platform:
        description: "Platform hosting this deployment."
        $ref: "#/definitions/DeploymentPlatform"
    description: "The period during which some deployable was active in a runtime."
    example:
      user_email: "user_email"
      address: "address"
      resource_uri:
      - "resource_uri"
      - "resource_uri"
      undeploy_time: "2000-01-23T04:56:07.000+00:00"
      deploy_time: "2000-01-23T04:56:07.000+00:00"
      config: "config"
      platform: {}
  discoveryDiscovered:
    type: "object"
    properties:
      continuous_analysis:
        description: "Whether the resource is continuously analyzed."
        $ref: "#/definitions/DiscoveredContinuousAnalysis"
      last_analysis_time:
        type: "string"
        format: "date-time"
        description: "The last time continuous analysis was done for this resource.\n\
          Deprecated, do not use."
      analysis_status:
        description: "The status of discovery for the resource."
        $ref: "#/definitions/DiscoveredAnalysisStatus"
      analysis_status_error:
        description: "When an error is encountered this will contain a LocalizedMessage\
          \ under\ndetails to show to the user. The LocalizedMessage is output only\
          \ and\npopulated by the API."
        $ref: "#/definitions/rpcStatus"
    description: "Provides information about the analysis status of a discovered resource."
    example:
      last_analysis_time: "2000-01-23T04:56:07.000+00:00"
      analysis_status: {}
      continuous_analysis: {}
      analysis_status_error:
        code: 1
        details:
        - value: "value"
          type_url: "type_url"
        - value: "value"
          type_url: "type_url"
        message: "message"
  discoveryDiscovery:
    type: "object"
    properties:
      analysis_kind:
        description: "Required. Immutable. The kind of analysis that is handled by\
          \ this\ndiscovery."
        $ref: "#/definitions/v1beta1NoteKind"
    description: "A note that indicates a type of analysis a provider would perform.\
      \ This note\nexists in a provider's project. A `Discovery` occurrence is created\
      \ in a\nconsumer's project at the start of analysis."
    example: {}
  grafeasv1beta1Signature:
    type: "object"
    properties:
      signature:
        type: "string"
        format: "byte"
        description: "The content of the signature, an opaque bytestring.\nThe payload\
          \ that this signature verifies MUST be unambiguously provided\nwith the\
          \ Signature during verification. A wrapper message might provide\nthe payload\
          \ explicitly. Alternatively, a message might have a canonical\nserialization\
          \ that can always be unambiguously computed to derive the\npayload."
        pattern: "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"
      public_key_id:
        type: "string"
        description: "The identifier for the public key that verifies this signature.\n\
          \  * The `public_key_id` is required.\n  * The `public_key_id` SHOULD be\
          \ an RFC3986 conformant URI.\n  * When possible, the `public_key_id` SHOULD\
          \ be an immutable reference,\n    such as a cryptographic digest.\n\nExamples\
          \ of valid `public_key_id`s:\n\nOpenPGP V4 public key fingerprint:\n  *\
          \ \"openpgp4fpr:74FAF3B861BDA0870C7B6DEF607E48D2A663AEEA\"\nSee https://www.iana.org/assignments/uri-schemes/prov/openpgp4fpr\
          \ for more\ndetails on this scheme.\n\nRFC6920 digest-named SubjectPublicKeyInfo\
          \ (digest of the DER\nserialization):\n  * \"ni:///sha-256;cD9o9Cq6LG3jD0iKXqEi_vdjJGecm_iXkbqVoScViaU\"\
          \n  * \"nih:///sha-256;703f68f42aba2c6de30f488a5ea122fef76324679c9bf89791ba95a1271589a5\""
    description: "Verifiers (e.g. Kritis implementations) MUST verify signatures\n\
      with respect to the trust anchors defined in policy (e.g. a Kritis policy).\n\
      Typically this means that the verifier has been configured with a map from\n\
      `public_key_id` to public key material (and any required parameters, e.g.\n\
      signing algorithm).\n\nIn particular, verification implementations MUST NOT\
      \ treat the signature\n`public_key_id` as anything more than a key lookup hint.\
      \ The `public_key_id`\nDOES NOT validate or authenticate a public key; it only\
      \ provides a mechanism\nfor quickly selecting a public key ALREADY CONFIGURED\
      \ on the verifier through\na trusted channel. Verification implementations MUST\
      \ reject signatures in any\nof the following circumstances:\n  * The `public_key_id`\
      \ is not recognized by the verifier.\n  * The public key that `public_key_id`\
      \ refers to does not verify the\n    signature with respect to the payload.\n\
      \nThe `signature` contents SHOULD NOT be \"attached\" (where the payload is\n\
      included with the serialized `signature` bytes). Verifiers MUST ignore any\n\
      \"attached\" payload and only verify signatures with respect to explicitly\n\
      provided payload (e.g. a `payload` field on the proto message that holds\nthis\
      \ Signature, or the canonical serialization of the proto message that\nholds\
      \ this signature)."
    example:
      signature: "signature"
      public_key_id: "public_key_id"
  imageBasis:
    type: "object"
    properties:
      resource_url:
        type: "string"
        description: "Required. Immutable. The resource_url for the resource representing\
          \ the\nbasis of associated occurrence images."
      fingerprint:
        description: "Required. Immutable. The fingerprint of the base image."
        $ref: "#/definitions/imageFingerprint"
    description: "Basis describes the base image portion (Note) of the DockerImage\n\
      relationship. Linked occurrences are derived from this or an\nequivalent image\
      \ via:\n  FROM <Basis.resource_url>\nOr an equivalent reference, e.g. a tag\
      \ of the resource_url."
    example:
      resource_url: "resource_url"
      fingerprint:
        v2_name: "v2_name"
        v1_name: "v1_name"
        v2_blob:
        - "v2_blob"
        - "v2_blob"
  imageDerived:
    type: "object"
    properties:
      fingerprint:
        description: "Required. The fingerprint of the derived image."
        $ref: "#/definitions/imageFingerprint"
      distance:
        type: "integer"
        format: "int32"
        description: "Output only. The number of layers by which this image differs\
          \ from the\nassociated image basis."
        readOnly: true
      layer_info:
        type: "array"
        description: "This contains layer-specific metadata, if populated it has length\n\
          \"distance\" and is ordered with [distance] being the layer immediately\n\
          following the base image and [1] being the final layer."
        items:
          $ref: "#/definitions/imageLayer"
      base_resource_url:
        type: "string"
        description: "Output only. This contains the base image URL for the derived\
          \ image\noccurrence."
        readOnly: true
    description: "Derived describes the derived image portion (Occurrence) of the\
      \ DockerImage\nrelationship. This image would be produced from a Dockerfile\
      \ with FROM\n<DockerImage.Basis in attached Note>."
    example:
      distance: 6
      base_resource_url: "base_resource_url"
      fingerprint:
        v2_name: "v2_name"
        v1_name: "v1_name"
        v2_blob:
        - "v2_blob"
        - "v2_blob"
      layer_info:
      - arguments: "arguments"
        directive: {}
      - arguments: "arguments"
        directive: {}
  imageFingerprint:
    type: "object"
    properties:
      v1_name:
        type: "string"
        description: "Required. The layer ID of the final layer in the Docker image's\
          \ v1\nrepresentation."
      v2_blob:
        type: "array"
        description: "Required. The ordered list of v2 blobs that represent a given\
          \ image."
        items:
          type: "string"
      v2_name:
        type: "string"
        description: "Output only. The name of the image's v2 blobs computed via:\n\
          \  [bottom] := v2_blob[bottom]\n  [N] := sha256(v2_blob[N] + \" \" + v2_name[N+1])\n\
          Only the name of the final blob is kept."
        readOnly: true
    description: "A set of properties that uniquely identify a given Docker image."
    example:
      v2_name: "v2_name"
      v1_name: "v1_name"
      v2_blob:
      - "v2_blob"
      - "v2_blob"
  imageLayer:
    type: "object"
    properties:
      directive:
        description: "Required. The recovered Dockerfile directive used to construct\
          \ this layer."
        $ref: "#/definitions/LayerDirective"
      arguments:
        type: "string"
        description: "The recovered arguments to the Dockerfile directive."
    description: "Layer holds metadata specific to a layer of a Docker image."
    example:
      arguments: "arguments"
      directive: {}
  intotoInToto:
    type: "object"
    properties:
      step_name:
        type: "string"
        description: "This field identifies the name of the step in the supply chain."
      signing_keys:
        type: "array"
        description: "This field contains the public keys that can be used to verify\
          \ the\nsignatures on the step metadata."
        items:
          $ref: "#/definitions/intotoSigningKey"
      expected_materials:
        type: "array"
        description: "The following fields contain in-toto artifact rules identifying\
          \ the\nartifacts that enter this supply chain step, and exit the supply\
          \ chain\nstep, i.e. materials and products of the step."
        items:
          $ref: "#/definitions/InTotoArtifactRule"
      expected_products:
        type: "array"
        items:
          $ref: "#/definitions/InTotoArtifactRule"
      expected_command:
        type: "array"
        description: "This field contains the expected command used to perform the\
          \ step."
        items:
          type: "string"
      threshold:
        type: "string"
        format: "int64"
        description: "This field contains a value that indicates the minimum number\
          \ of keys that\nneed to be used to sign the step's in-toto link."
    description: "This contains the fields corresponding to the definition of a software\
      \ supply\nchain step in an in-toto layout. This information goes into a Grafeas\
      \ note."
    example:
      expected_products:
      - artifact_rule:
        - "artifact_rule"
        - "artifact_rule"
      - artifact_rule:
        - "artifact_rule"
        - "artifact_rule"
      step_name: "step_name"
      signing_keys:
      - key_type: "key_type"
        key_scheme: "key_scheme"
        key_id: "key_id"
        public_key_value: "public_key_value"
      - key_type: "key_type"
        key_scheme: "key_scheme"
        key_id: "key_id"
        public_key_value: "public_key_value"
      expected_command:
      - "expected_command"
      - "expected_command"
      threshold: "threshold"
      expected_materials:
      - artifact_rule:
        - "artifact_rule"
        - "artifact_rule"
      - artifact_rule:
        - "artifact_rule"
        - "artifact_rule"
  intotoLink:
    type: "object"
    properties:
      effective_command:
        type: "array"
        title: "This field contains the full command executed for the step. This can\
          \ also\nbe empty if links are generated for operations that aren't directly\
          \ mapped\nto a specific command. Each term in the command is an independent\
          \ string\nin the list. An example of a command in the in-toto metadata field\
          \ is:\n\"command\": [\"git\", \"clone\", \"https://github.com/in-toto/demo-project.git\"\
          ]"
        items:
          type: "string"
      materials:
        type: "array"
        title: "Materials are the supply chain artifacts that go into the step and\
          \ are used\nfor the operation performed. The key of the map is the path\
          \ of the artifact\nand the structure contains the recorded hash information.\
          \ An example is:\n\"materials\": [\n  {\n    \"resource_uri\": \"foo/bar\"\
          ,\n    \"hashes\": {\n      \"sha256\": \"ebebf...\",\n      <OTHER HASH\
          \ ALGORITHMS>: <HASH VALUE>\n    }\n  }\n]"
        items:
          $ref: "#/definitions/intotoLinkArtifact"
      products:
        type: "array"
        description: "Products are the supply chain artifacts generated as a result\
          \ of the step.\nThe structure is identical to that of materials."
        items:
          $ref: "#/definitions/intotoLinkArtifact"
      byproducts:
        description: "ByProducts are data generated as part of a software supply chain\
          \ step, but\nare not the actual result of the step."
        $ref: "#/definitions/LinkByProducts"
      environment:
        title: "This is a field that can be used to capture information about the\n\
          environment. It is suggested for this field to contain information that\n\
          details environment variables, filesystem information, and the present\n\
          working directory. The recommended structure of this field is:\n\"environment\"\
          : {\n  \"custom_values\": {\n    \"variables\": \"<ENV>\",\n    \"filesystem\"\
          : \"<FS>\",\n    \"workdir\": \"<CWD>\",\n    \"<ANY OTHER RELEVANT FIELDS>\"\
          : \"...\"\n  }\n}"
        $ref: "#/definitions/LinkEnvironment"
    description: "This corresponds to an in-toto link."
    example:
      environment:
        custom_values:
          key: "custom_values"
      effective_command:
      - "effective_command"
      - "effective_command"
      materials:
      - resource_uri: "resource_uri"
        hashes:
          sha256: "sha256"
      - resource_uri: "resource_uri"
        hashes:
          sha256: "sha256"
      byproducts:
        custom_values:
          key: "custom_values"
      products:
      - resource_uri: "resource_uri"
        hashes:
          sha256: "sha256"
      - resource_uri: "resource_uri"
        hashes:
          sha256: "sha256"
  intotoLinkArtifact:
    type: "object"
    properties:
      resource_uri:
        type: "string"
      hashes:
        $ref: "#/definitions/LinkArtifactHashes"
    example:
      resource_uri: "resource_uri"
      hashes:
        sha256: "sha256"
  intotoSigningKey:
    type: "object"
    properties:
      key_id:
        type: "string"
        description: "key_id is an identifier for the signing key."
      key_type:
        type: "string"
        description: "This field identifies the specific signing method. Eg: \"rsa\"\
          , \"ed25519\",\nand \"ecdsa\"."
      public_key_value:
        type: "string"
        description: "This field contains the actual public key."
      key_scheme:
        type: "string"
        description: "This field contains the corresponding signature scheme.\nEg:\
          \ \"rsassa-pss-sha256\"."
    description: "This defines the format used to record keys used in the software\
      \ supply\nchain. An in-toto link is attested using one or more keys defined\
      \ in the\nin-toto layout. An example of this is:\n{\n  \"key_id\": \"776a00e29f3559e0141b3b096f696abc6cfb0c657ab40f441132b345b0...\"\
      ,\n  \"key_type\": \"rsa\",\n  \"public_key_value\": \"-----BEGIN PUBLIC KEY-----\\\
      nMIIBojANBgkqhkiG9w0B...\",\n  \"key_scheme\": \"rsassa-pss-sha256\"\n}\nThe\
      \ format for in-toto's key definition can be found in section 4.2 of the\nin-toto\
      \ specification."
    example:
      key_type: "key_type"
      key_scheme: "key_scheme"
      key_id: "key_id"
      public_key_value: "public_key_value"
  packageArchitecture:
    type: "string"
    description: "Instruction set architectures supported by various package managers.\n\
      \n - ARCHITECTURE_UNSPECIFIED: Unknown architecture.\n - X86: X86 architecture.\n\
      \ - X64: X64 architecture."
    enum:
    - "ARCHITECTURE_UNSPECIFIED"
    - "X86"
    - "X64"
    default: "ARCHITECTURE_UNSPECIFIED"
  packageDistribution:
    type: "object"
    properties:
      cpe_uri:
        type: "string"
        description: "Required. The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)\n\
          denoting the package manager version distributing a package."
      architecture:
        description: "The CPU architecture for which packages in this distribution\
          \ channel were\nbuilt."
        $ref: "#/definitions/packageArchitecture"
      latest_version:
        description: "The latest available version of this package in this distribution\
          \ channel."
        $ref: "#/definitions/packageVersion"
      maintainer:
        type: "string"
        description: "A freeform string denoting the maintainer of this package."
      url:
        type: "string"
        description: "The distribution channel-specific homepage for this package."
      description:
        type: "string"
        description: "The distribution channel-specific description of this package."
    description: "This represents a particular channel of distribution for a given\
      \ package.\nE.g., Debian's jessie-backports dpkg mirror."
    example:
      latest_version:
        kind: {}
        name: "name"
        epoch: 6
        revision: "revision"
      cpe_uri: "cpe_uri"
      description: "description"
      maintainer: "maintainer"
      url: "url"
      architecture: {}
  packageInstallation:
    type: "object"
    properties:
      name:
        type: "string"
        description: "Output only. The name of the installed package."
        readOnly: true
      location:
        type: "array"
        description: "Required. All of the places within the filesystem versions of\
          \ this package\nhave been found."
        items:
          $ref: "#/definitions/v1beta1packageLocation"
    description: "This represents how a particular software package may be installed\
      \ on a\nsystem."
    example:
      name: "name"
      location:
      - path: "path"
        cpe_uri: "cpe_uri"
        version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
      - path: "path"
        cpe_uri: "cpe_uri"
        version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
  packagePackage:
    type: "object"
    properties:
      name:
        type: "string"
        description: "Required. Immutable. The name of the package."
      distribution:
        type: "array"
        description: "The various channels by which a package is distributed."
        items:
          $ref: "#/definitions/packageDistribution"
    description: "This represents a particular package that is distributed over various\n\
      channels. E.g., glibc (aka libc6) is distributed by many, at various\nversions."
    example:
      name: "name"
      distribution:
      - latest_version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
        cpe_uri: "cpe_uri"
        description: "description"
        maintainer: "maintainer"
        url: "url"
        architecture: {}
      - latest_version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
        cpe_uri: "cpe_uri"
        description: "description"
        maintainer: "maintainer"
        url: "url"
        architecture: {}
  packageVersion:
    type: "object"
    properties:
      epoch:
        type: "integer"
        format: "int32"
        description: "Used to correct mistakes in the version numbering scheme."
      name:
        type: "string"
        description: "Required only when version kind is NORMAL. The main part of\
          \ the version\nname."
      revision:
        type: "string"
        description: "The iteration of the package build from the above version."
      kind:
        description: "Required. Distinguishes between sentinel MIN/MAX versions and\
          \ normal\nversions."
        $ref: "#/definitions/VersionVersionKind"
    description: "Version contains structured information about the version of a package."
    example:
      kind: {}
      name: "name"
      epoch: 6
      revision: "revision"
  protobufAny:
    type: "object"
    properties:
      type_url:
        type: "string"
        description: "A URL/resource name that uniquely identifies the type of the\
          \ serialized\nprotocol buffer message. This string must contain at least\n\
          one \"/\" character. The last segment of the URL's path must represent\n\
          the fully qualified name of the type (as in\n`path/google.protobuf.Duration`).\
          \ The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\
          \nIn practice, teams usually precompile into the binary all types that they\n\
          expect it to use in the context of Any. However, for URLs which use the\n\
          scheme `http`, `https`, or no scheme, one can optionally set up a type\n\
          server that maps type URLs to message definitions as follows:\n\n* If no\
          \ scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must\
          \ yield a [google.protobuf.Type][]\n  value in binary format, or produce\
          \ an error.\n* Applications are allowed to cache lookup results based on\
          \ the\n  URL, or have them precompiled into a binary to avoid any\n  lookup.\
          \ Therefore, binary compatibility needs to be preserved\n  on changes to\
          \ types. (Use versioned type names to manage\n  breaking changes.)\n\nNote:\
          \ this functionality is not currently available in the official\nprotobuf\
          \ release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\
          \nSchemes other than `http`, `https` (or the empty scheme) might be\nused\
          \ with implementation specific semantics."
      value:
        type: "string"
        format: "byte"
        description: "Must be a valid serialized protocol buffer of the above specified\
          \ type."
        pattern: "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"
    description: "`Any` contains an arbitrary serialized protocol buffer message along\
      \ with a\nURL that describes the type of the serialized message.\n\nProtobuf\
      \ library provides support to pack/unpack Any values in the form\nof utility\
      \ functions or additional generated methods of the Any type.\n\nExample 1: Pack\
      \ and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n\
      \    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack\
      \ and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n\
      \    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n\
      \    }\n\n Example 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n\
      \    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n\
      \      any.Unpack(foo)\n      ...\n\n Example 4: Pack and unpack a message in\
      \ Go\n\n     foo := &pb.Foo{...}\n     any, err := ptypes.MarshalAny(foo)\n\
      \     ...\n     foo := &pb.Foo{}\n     if err := ptypes.UnmarshalAny(any, foo);\
      \ err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf\
      \ library will by default use\n'type.googleapis.com/full.type.name' as the type\
      \ URL and the unpack\nmethods only use the fully qualified type name after the\
      \ last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\n\
      name \"y.z\".\n\n\nJSON\n====\nThe JSON representation of an `Any` value uses\
      \ the regular\nrepresentation of the deserialized, embedded message, with an\n\
      additional field `@type` which contains the type URL. Example:\n\n    package\
      \ google.profile;\n    message Person {\n      string first_name = 1;\n    \
      \  string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\"\
      ,\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf\
      \ the embedded message type is well-known and has a custom JSON\nrepresentation,\
      \ that representation will be embedded adding a field\n`value` which holds the\
      \ custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\
      \n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n\
      \      \"value\": \"1.212s\"\n    }"
    example:
      value: "value"
      type_url: "type_url"
  protobufFieldMask:
    type: "object"
    properties:
      paths:
        type: "array"
        description: "The set of field mask paths."
        items:
          type: "string"
    title: "`FieldMask` represents a set of symbolic field paths, for example:"
    description: "paths: \"f.a\"\n    paths: \"f.b.d\"\n\nHere `f` represents a field\
      \ in some root message, `a` and `b`\nfields in the message found in `f`, and\
      \ `d` a field found in the\nmessage in `f.b`.\n\nField masks are used to specify\
      \ a subset of fields that should be\nreturned by a get operation or modified\
      \ by an update operation.\nField masks also have a custom JSON encoding (see\
      \ below).\n\n# Field Masks in Projections\n\nWhen used in the context of a projection,\
      \ a response message or\nsub-message is filtered by the API to only contain\
      \ those fields as\nspecified in the mask. For example, if the mask in the previous\n\
      example is applied to a response message as follows:\n\n    f {\n      a : 22\n\
      \      b {\n        d : 1\n        x : 2\n      }\n      y : 13\n    }\n   \
      \ z: 8\n\nThe result will not contain specific values for fields x,y and z\n\
      (their value will be set to the default, and omitted in proto text\noutput):\n\
      \n\n    f {\n      a : 22\n      b {\n        d : 1\n      }\n    }\n\nA repeated\
      \ field is not allowed except at the last position of a\npaths string.\n\nIf\
      \ a FieldMask object is not present in a get operation, the\noperation applies\
      \ to all fields (as if a FieldMask of all fields\nhad been specified).\n\nNote\
      \ that a field mask does not necessarily apply to the\ntop-level response message.\
      \ In case of a REST get operation, the\nfield mask applies directly to the response,\
      \ but in case of a REST\nlist operation, the mask instead applies to each individual\
      \ message\nin the returned resource list. In case of a REST custom method,\n\
      other definitions may be used. Where the mask applies will be\nclearly documented\
      \ together with its declaration in the API.  In\nany case, the effect on the\
      \ returned resource/resources is required\nbehavior for APIs.\n\n# Field Masks\
      \ in Update Operations\n\nA field mask in update operations specifies which\
      \ fields of the\ntargeted resource are going to be updated. The API is required\n\
      to only change the values of the fields as specified in the mask\nand leave\
      \ the others untouched. If a resource is passed in to\ndescribe the updated\
      \ values, the API ignores the values of all\nfields not covered by the mask.\n\
      \nIf a repeated field is specified for an update operation, new values will\n\
      be appended to the existing repeated field in the target resource. Note that\n\
      a repeated field is only allowed in the last position of a `paths` string.\n\
      \nIf a sub-message is specified in the last position of the field mask for an\n\
      update operation, then new value will be merged into the existing sub-message\n\
      in the target resource.\n\nFor example, given the target message:\n\n    f {\n\
      \      b {\n        d: 1\n        x: 2\n      }\n      c: [1]\n    }\n\nAnd\
      \ an update message:\n\n    f {\n      b {\n        d: 10\n      }\n      c:\
      \ [2]\n    }\n\nthen if the field mask is:\n\n paths: [\"f.b\", \"f.c\"]\n\n\
      then the result will be:\n\n    f {\n      b {\n        d: 10\n        x: 2\n\
      \      }\n      c: [1, 2]\n    }\n\nAn implementation may provide options to\
      \ override this default behavior for\nrepeated and message fields.\n\nIn order\
      \ to reset a field's value to the default, the field must\nbe in the mask and\
      \ set to the default value in the provided resource.\nHence, in order to reset\
      \ all fields of a resource, provide a default\ninstance of the resource and\
      \ set all fields in the mask, or do\nnot provide a mask as described below.\n\
      \nIf a field mask is not present on update, the operation applies to\nall fields\
      \ (as if a field mask of all fields has been specified).\nNote that in the presence\
      \ of schema evolution, this may mean that\nfields the client does not know and\
      \ has therefore not filled into\nthe request will be reset to their default.\
      \ If this is unwanted\nbehavior, a specific service may require a client to\
      \ always specify\na field mask, producing an error if not.\n\nAs with get operations,\
      \ the location of the resource which\ndescribes the updated values in the request\
      \ message depends on the\noperation kind. In any case, the effect of the field\
      \ mask is\nrequired to be honored by the API.\n\n## Considerations for HTTP\
      \ REST\n\nThe HTTP kind of an update operation which uses a field mask must\n\
      be set to PATCH instead of PUT in order to satisfy HTTP semantics\n(PUT must\
      \ only be used for full updates).\n\n# JSON Encoding of Field Masks\n\nIn JSON,\
      \ a field mask is encoded as a single string where paths are\nseparated by a\
      \ comma. Fields name in each path are converted\nto/from lower-camel naming\
      \ conventions.\n\nAs an example, consider the following message declarations:\n\
      \n    message Profile {\n      User user = 1;\n      Photo photo = 2;\n    }\n\
      \    message User {\n      string display_name = 1;\n      string address =\
      \ 2;\n    }\n\nIn proto a field mask for `Profile` may look as such:\n\n   \
      \ mask {\n      paths: \"user.display_name\"\n      paths: \"photo\"\n    }\n\
      \nIn JSON, the same mask is represented as below:\n\n    {\n      mask: \"user.displayName,photo\"\
      \n    }\n\n# Field Masks and Oneof Fields\n\nField masks treat fields in oneofs\
      \ just as regular fields. Consider the\nfollowing message:\n\n    message SampleMessage\
      \ {\n      oneof test_oneof {\n        string name = 4;\n        SubMessage\
      \ sub_message = 9;\n      }\n    }\n\nThe field mask can be:\n\n    mask {\n\
      \      paths: \"name\"\n    }\n\nOr:\n\n    mask {\n      paths: \"sub_message\"\
      \n    }\n\nNote that oneof type names (\"test_oneof\" in this case) cannot be\
      \ used in\npaths.\n\n## Field Mask Verification\n\nThe implementation of any\
      \ API method which has a FieldMask type field in the\nrequest should verify\
      \ the included field paths, and return an\n`INVALID_ARGUMENT` error if any path\
      \ is duplicated or unmappable."
  provenanceBuildProvenance:
    type: "object"
    properties:
      id:
        type: "string"
        description: "Required. Unique identifier of the build."
      project_id:
        type: "string"
        description: "ID of the project."
      commands:
        type: "array"
        description: "Commands requested by the build."
        items:
          $ref: "#/definitions/provenanceCommand"
      built_artifacts:
        type: "array"
        description: "Output of the build."
        items:
          $ref: "#/definitions/v1beta1provenanceArtifact"
      create_time:
        type: "string"
        format: "date-time"
        description: "Time at which the build was created."
      start_time:
        type: "string"
        format: "date-time"
        description: "Time at which execution of the build was started."
      end_time:
        type: "string"
        format: "date-time"
        description: "Time at which execution of the build was finished."
      creator:
        type: "string"
        description: "E-mail address of the user who initiated this build. Note that\
          \ this was the\nuser's e-mail address at the time the build was initiated;\
          \ this address may\nnot represent the same end-user for all time."
      logs_uri:
        type: "string"
        description: "URI where any logs for this provenance were written."
      source_provenance:
        description: "Details of the Source input to the build."
        $ref: "#/definitions/provenanceSource"
      trigger_id:
        type: "string"
        description: "Trigger identifier if the build was triggered automatically;\
          \ empty if not."
      build_options:
        type: "object"
        description: "Special options applied to this build. This is a catch-all field\
          \ where\nbuild providers can enter any desired additional details."
        additionalProperties:
          type: "string"
      builder_version:
        type: "string"
        description: "Version string of the builder at the time this build was executed."
    description: "Provenance of a build. Contains all information needed to verify\
      \ the full\ndetails about the build from source to completion."
    example:
      creator: "creator"
      create_time: "2000-01-23T04:56:07.000+00:00"
      trigger_id: "trigger_id"
      end_time: "2000-01-23T04:56:07.000+00:00"
      built_artifacts:
      - names:
        - "names"
        - "names"
        checksum: "checksum"
        id: "id"
      - names:
        - "names"
        - "names"
        checksum: "checksum"
        id: "id"
      logs_uri: "logs_uri"
      builder_version: "builder_version"
      source_provenance:
        artifact_storage_source_uri: "artifact_storage_source_uri"
        additional_contexts:
        - git:
            url: "url"
            revision_id: "revision_id"
          cloud_repo:
            repo_id:
              uid: "uid"
              project_repo_id:
                project_id: "project_id"
                repo_name: "repo_name"
            alias_context:
              kind: {}
              name: "name"
            revision_id: "revision_id"
          gerrit:
            gerrit_project: "gerrit_project"
            alias_context:
              kind: {}
              name: "name"
            host_uri: "host_uri"
            revision_id: "revision_id"
          labels:
            key: "labels"
        - git:
            url: "url"
            revision_id: "revision_id"
          cloud_repo:
            repo_id:
              uid: "uid"
              project_repo_id:
                project_id: "project_id"
                repo_name: "repo_name"
            alias_context:
              kind: {}
              name: "name"
            revision_id: "revision_id"
          gerrit:
            gerrit_project: "gerrit_project"
            alias_context:
              kind: {}
              name: "name"
            host_uri: "host_uri"
            revision_id: "revision_id"
          labels:
            key: "labels"
        file_hashes:
          key:
            file_hash:
            - type: {}
              value: "value"
            - type: {}
              value: "value"
        context:
          git:
            url: "url"
            revision_id: "revision_id"
          cloud_repo:
            repo_id:
              uid: "uid"
              project_repo_id:
                project_id: "project_id"
                repo_name: "repo_name"
            alias_context:
              kind: {}
              name: "name"
            revision_id: "revision_id"
          gerrit:
            gerrit_project: "gerrit_project"
            alias_context:
              kind: {}
              name: "name"
            host_uri: "host_uri"
            revision_id: "revision_id"
          labels:
            key: "labels"
      start_time: "2000-01-23T04:56:07.000+00:00"
      project_id: "project_id"
      id: "id"
      commands:
      - args:
        - "args"
        - "args"
        name: "name"
        id: "id"
        wait_for:
        - "wait_for"
        - "wait_for"
        env:
        - "env"
        - "env"
        dir: "dir"
      - args:
        - "args"
        - "args"
        name: "name"
        id: "id"
        wait_for:
        - "wait_for"
        - "wait_for"
        env:
        - "env"
        - "env"
        dir: "dir"
      build_options:
        key: "build_options"
  provenanceCommand:
    type: "object"
    properties:
      name:
        type: "string"
        description: "Required. Name of the command, as presented on the command line,\
          \ or if the\ncommand is packaged as a Docker container, as presented to\
          \ `docker pull`."
      env:
        type: "array"
        description: "Environment variables set before running this command."
        items:
          type: "string"
      args:
        type: "array"
        description: "Command-line arguments used when executing this command."
        items:
          type: "string"
      dir:
        type: "string"
        description: "Working directory (relative to project source root) used when\
          \ running this\ncommand."
      id:
        type: "string"
        description: "Optional unique identifier for this command, used in wait_for\
          \ to reference\nthis command as a dependency."
      wait_for:
        type: "array"
        description: "The ID(s) of the command(s) that this command depends on."
        items:
          type: "string"
    description: "Command describes a step performed as part of the build pipeline."
    example:
      args:
      - "args"
      - "args"
      name: "name"
      id: "id"
      wait_for:
      - "wait_for"
      - "wait_for"
      env:
      - "env"
      - "env"
      dir: "dir"
  provenanceFileHashes:
    type: "object"
    properties:
      file_hash:
        type: "array"
        description: "Required. Collection of file hashes."
        items:
          $ref: "#/definitions/provenanceHash"
    description: "Container message for hashes of byte content of files, used in source\n\
      messages to verify integrity of source input to the build."
    example:
      file_hash:
      - type: {}
        value: "value"
      - type: {}
        value: "value"
  provenanceHash:
    type: "object"
    properties:
      type:
        description: "Required. The type of hash that was performed."
        $ref: "#/definitions/HashHashType"
      value:
        type: "string"
        format: "byte"
        description: "Required. The hash value."
        pattern: "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"
    description: "Container message for hash values."
    example:
      type: {}
      value: "value"
  provenanceSource:
    type: "object"
    properties:
      artifact_storage_source_uri:
        type: "string"
        description: "If provided, the input binary artifacts for the build came from\
          \ this\nlocation."
      file_hashes:
        type: "object"
        description: "Hash(es) of the build source, which can be used to verify that\
          \ the original\nsource integrity was maintained in the build.\n\nThe keys\
          \ to this map are file paths used as build source and the values\ncontain\
          \ the hash values for those files.\n\nIf the build source came in a single\
          \ package such as a gzipped tarfile\n(.tar.gz), the FileHash will be for\
          \ the single path to that file."
        additionalProperties:
          $ref: "#/definitions/provenanceFileHashes"
      context:
        description: "If provided, the source code used for the build came from this\
          \ location."
        $ref: "#/definitions/sourceSourceContext"
      additional_contexts:
        type: "array"
        description: "If provided, some of the source code used for the build may\
          \ be found in\nthese locations, in the case where the source repository\
          \ had multiple\nremotes or submodules. This list will not include the context\
          \ specified in\nthe context field."
        items:
          $ref: "#/definitions/sourceSourceContext"
    description: "Source describes the location of the source used for the build."
    example:
      artifact_storage_source_uri: "artifact_storage_source_uri"
      additional_contexts:
      - git:
          url: "url"
          revision_id: "revision_id"
        cloud_repo:
          repo_id:
            uid: "uid"
            project_repo_id:
              project_id: "project_id"
              repo_name: "repo_name"
          alias_context:
            kind: {}
            name: "name"
          revision_id: "revision_id"
        gerrit:
          gerrit_project: "gerrit_project"
          alias_context:
            kind: {}
            name: "name"
          host_uri: "host_uri"
          revision_id: "revision_id"
        labels:
          key: "labels"
      - git:
          url: "url"
          revision_id: "revision_id"
        cloud_repo:
          repo_id:
            uid: "uid"
            project_repo_id:
              project_id: "project_id"
              repo_name: "repo_name"
          alias_context:
            kind: {}
            name: "name"
          revision_id: "revision_id"
        gerrit:
          gerrit_project: "gerrit_project"
          alias_context:
            kind: {}
            name: "name"
          host_uri: "host_uri"
          revision_id: "revision_id"
        labels:
          key: "labels"
      file_hashes:
        key:
          file_hash:
          - type: {}
            value: "value"
          - type: {}
            value: "value"
      context:
        git:
          url: "url"
          revision_id: "revision_id"
        cloud_repo:
          repo_id:
            uid: "uid"
            project_repo_id:
              project_id: "project_id"
              repo_name: "repo_name"
          alias_context:
            kind: {}
            name: "name"
          revision_id: "revision_id"
        gerrit:
          gerrit_project: "gerrit_project"
          alias_context:
            kind: {}
            name: "name"
          host_uri: "host_uri"
          revision_id: "revision_id"
        labels:
          key: "labels"
  rpcStatus:
    type: "object"
    properties:
      code:
        type: "integer"
        format: "int32"
        description: "The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code]."
      message:
        type: "string"
        description: "A developer-facing error message, which should be in English.\
          \ Any\nuser-facing error message should be localized and sent in the\n[google.rpc.Status.details][google.rpc.Status.details]\
          \ field, or localized by the client."
      details:
        type: "array"
        description: "A list of messages that carry the error details.  There is a\
          \ common set of\nmessage types for APIs to use."
        items:
          $ref: "#/definitions/protobufAny"
    title: "The `Status` type defines a logical error model that is suitable for different\n\
      programming environments, including REST APIs and RPC APIs. It is used by\n\
      [gRPC](https://github.com/grpc). The error model is designed to be:"
    description: "- Simple to use and understand for most users\n- Flexible enough\
      \ to meet unexpected needs\n\n# Overview\n\nThe `Status` message contains three\
      \ pieces of data: error code, error message,\nand error details. The error code\
      \ should be an enum value of\n[google.rpc.Code][google.rpc.Code], but it may\
      \ accept additional error codes if needed.  The\nerror message should be a developer-facing\
      \ English message that helps\ndevelopers *understand* and *resolve* the error.\
      \ If a localized user-facing\nerror message is needed, put the localized message\
      \ in the error details or\nlocalize it in the client. The optional error details\
      \ may contain arbitrary\ninformation about the error. There is a predefined\
      \ set of error detail types\nin the package `google.rpc` that can be used for\
      \ common error conditions.\n\n# Language mapping\n\nThe `Status` message is\
      \ the logical representation of the error model, but it\nis not necessarily\
      \ the actual wire format. When the `Status` message is\nexposed in different\
      \ client libraries and different wire protocols, it can be\nmapped differently.\
      \ For example, it will likely be mapped to some exceptions\nin Java, but more\
      \ likely mapped to some error codes in C.\n\n# Other uses\n\nThe error model\
      \ and the `Status` message can be used in a variety of\nenvironments, either\
      \ with or without APIs, to provide a\nconsistent developer experience across\
      \ different environments.\n\nExample uses of this error model include:\n\n-\
      \ Partial errors. If a service needs to return partial errors to the client,\n\
      \    it may embed the `Status` in the normal response to indicate the partial\n\
      \    errors.\n\n- Workflow errors. A typical workflow has multiple steps. Each\
      \ step may\n    have a `Status` message for error reporting.\n\n- Batch operations.\
      \ If a client uses batch request and batch response, the\n    `Status` message\
      \ should be used directly inside batch response, one for\n    each error sub-response.\n\
      \n- Asynchronous operations. If an API call embeds asynchronous operation\n\
      \    results in its response, the status of those operations should be\n   \
      \ represented directly using the `Status` message.\n\n- Logging. If some API\
      \ errors are stored in logs, the message `Status` could\n    be used directly\
      \ after any stripping needed for security/privacy reasons."
    example:
      code: 1
      details:
      - value: "value"
        type_url: "type_url"
      - value: "value"
        type_url: "type_url"
      message: "message"
  sourceAliasContext:
    type: "object"
    properties:
      kind:
        description: "The alias kind."
        $ref: "#/definitions/AliasContextKind"
      name:
        type: "string"
        description: "The alias name."
    description: "An alias to a repo revision."
    example:
      kind: {}
      name: "name"
  sourceCloudRepoSourceContext:
    type: "object"
    properties:
      repo_id:
        description: "The ID of the repo."
        $ref: "#/definitions/sourceRepoId"
      revision_id:
        type: "string"
        description: "A revision ID."
      alias_context:
        description: "An alias, which may be a branch or tag."
        $ref: "#/definitions/sourceAliasContext"
    description: "A CloudRepoSourceContext denotes a particular revision in a Google\
      \ Cloud\nSource Repo."
    example:
      repo_id:
        uid: "uid"
        project_repo_id:
          project_id: "project_id"
          repo_name: "repo_name"
      alias_context:
        kind: {}
        name: "name"
      revision_id: "revision_id"
  sourceGerritSourceContext:
    type: "object"
    properties:
      host_uri:
        type: "string"
        description: "The URI of a running Gerrit instance."
      gerrit_project:
        type: "string"
        description: "The full project name within the host. Projects may be nested,\
          \ so\n\"project/subproject\" is a valid project name. The \"repo name\"\
          \ is the\nhostURI/project."
      revision_id:
        type: "string"
        description: "A revision (commit) ID."
      alias_context:
        description: "An alias, which may be a branch or tag."
        $ref: "#/definitions/sourceAliasContext"
    description: "A SourceContext referring to a Gerrit project."
    example:
      gerrit_project: "gerrit_project"
      alias_context:
        kind: {}
        name: "name"
      host_uri: "host_uri"
      revision_id: "revision_id"
  sourceGitSourceContext:
    type: "object"
    properties:
      url:
        type: "string"
        description: "Git repository URL."
      revision_id:
        type: "string"
        description: "Git commit hash."
    description: "A GitSourceContext denotes a particular revision in a third party\
      \ Git\nrepository (e.g., GitHub)."
    example:
      url: "url"
      revision_id: "revision_id"
  sourceProjectRepoId:
    type: "object"
    properties:
      project_id:
        type: "string"
        description: "The ID of the project."
      repo_name:
        type: "string"
        description: "The name of the repo. Leave empty for the default repo."
    description: "Selects a repo using a Google Cloud Platform project ID (e.g.,\n\
      winged-cargo-31) and a repo name within that project."
    example:
      project_id: "project_id"
      repo_name: "repo_name"
  sourceRepoId:
    type: "object"
    properties:
      project_repo_id:
        description: "A combination of a project ID and a repo name."
        $ref: "#/definitions/sourceProjectRepoId"
      uid:
        type: "string"
        description: "A server-assigned, globally unique identifier."
    description: "A unique identifier for a Cloud Repo."
    example:
      uid: "uid"
      project_repo_id:
        project_id: "project_id"
        repo_name: "repo_name"
  sourceSourceContext:
    type: "object"
    properties:
      cloud_repo:
        description: "A SourceContext referring to a revision in a Google Cloud Source\
          \ Repo."
        $ref: "#/definitions/sourceCloudRepoSourceContext"
      gerrit:
        description: "A SourceContext referring to a Gerrit project."
        $ref: "#/definitions/sourceGerritSourceContext"
      git:
        description: "A SourceContext referring to any third party Git repo (e.g.,\
          \ GitHub)."
        $ref: "#/definitions/sourceGitSourceContext"
      labels:
        type: "object"
        description: "Labels with user defined metadata."
        additionalProperties:
          type: "string"
    description: "A SourceContext is a reference to a tree of files. A SourceContext\
      \ together\nwith a path point to a unique revision of a single file or directory."
    example:
      git:
        url: "url"
        revision_id: "revision_id"
      cloud_repo:
        repo_id:
          uid: "uid"
          project_repo_id:
            project_id: "project_id"
            repo_name: "repo_name"
        alias_context:
          kind: {}
          name: "name"
        revision_id: "revision_id"
      gerrit:
        gerrit_project: "gerrit_project"
        alias_context:
          kind: {}
          name: "name"
        host_uri: "host_uri"
        revision_id: "revision_id"
      labels:
        key: "labels"
  v1beta1BatchCreateNotesRequest:
    type: "object"
    properties:
      parent:
        type: "string"
        description: "The name of the project in the form of `projects/[PROJECT_ID]`,\
          \ under which\nthe notes are to be created."
      notes:
        type: "object"
        description: "The notes to create. Max allowed length is 1000."
        additionalProperties:
          $ref: "#/definitions/v1beta1Note"
    description: "Request to create notes in batch."
  v1beta1BatchCreateNotesResponse:
    type: "object"
    properties:
      notes:
        type: "array"
        description: "The notes that were created."
        items:
          $ref: "#/definitions/v1beta1Note"
    description: "Response for creating notes in batch."
    example:
      notes:
      - attestation_authority:
          hint:
            human_readable_name: "human_readable_name"
        short_description: "short_description"
        related_note_names:
        - "related_note_names"
        - "related_note_names"
        package:
          name: "name"
          distribution:
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
        create_time: "2000-01-23T04:56:07.000+00:00"
        kind: {}
        related_url:
        - label: "label"
          url: "url"
        - label: "label"
          url: "url"
        expiration_time: "2000-01-23T04:56:07.000+00:00"
        intoto:
          expected_products:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          step_name: "step_name"
          signing_keys:
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          expected_command:
          - "expected_command"
          - "expected_command"
          threshold: "threshold"
          expected_materials:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
        long_description: "long_description"
        vulnerability:
          severity: {}
          cvss_score: 0.8008282
          cvss_v3:
            attack_complexity: {}
            base_score: 1.4658129
            user_interaction: {}
            scope: {}
            impact_score: 5.637377
            confidentiality_impact: {}
            attack_vector: {}
            exploitability_score: 5.962134
            privileges_required: {}
          details:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          source_update_time: "2000-01-23T04:56:07.000+00:00"
          windows_details:
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
        update_time: "2000-01-23T04:56:07.000+00:00"
        base_image:
          resource_url: "resource_url"
          fingerprint:
            v2_name: "v2_name"
            v1_name: "v1_name"
            v2_blob:
            - "v2_blob"
            - "v2_blob"
        deployable:
          resource_uri:
          - "resource_uri"
          - "resource_uri"
        build:
          signature:
            public_key: "public_key"
            key_type: {}
            signature: "signature"
            key_id: "key_id"
          builder_version: "builder_version"
        discovery: {}
        name: "name"
      - attestation_authority:
          hint:
            human_readable_name: "human_readable_name"
        short_description: "short_description"
        related_note_names:
        - "related_note_names"
        - "related_note_names"
        package:
          name: "name"
          distribution:
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
        create_time: "2000-01-23T04:56:07.000+00:00"
        kind: {}
        related_url:
        - label: "label"
          url: "url"
        - label: "label"
          url: "url"
        expiration_time: "2000-01-23T04:56:07.000+00:00"
        intoto:
          expected_products:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          step_name: "step_name"
          signing_keys:
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          expected_command:
          - "expected_command"
          - "expected_command"
          threshold: "threshold"
          expected_materials:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
        long_description: "long_description"
        vulnerability:
          severity: {}
          cvss_score: 0.8008282
          cvss_v3:
            attack_complexity: {}
            base_score: 1.4658129
            user_interaction: {}
            scope: {}
            impact_score: 5.637377
            confidentiality_impact: {}
            attack_vector: {}
            exploitability_score: 5.962134
            privileges_required: {}
          details:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          source_update_time: "2000-01-23T04:56:07.000+00:00"
          windows_details:
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
        update_time: "2000-01-23T04:56:07.000+00:00"
        base_image:
          resource_url: "resource_url"
          fingerprint:
            v2_name: "v2_name"
            v1_name: "v1_name"
            v2_blob:
            - "v2_blob"
            - "v2_blob"
        deployable:
          resource_uri:
          - "resource_uri"
          - "resource_uri"
        build:
          signature:
            public_key: "public_key"
            key_type: {}
            signature: "signature"
            key_id: "key_id"
          builder_version: "builder_version"
        discovery: {}
        name: "name"
  v1beta1BatchCreateOccurrencesRequest:
    type: "object"
    properties:
      parent:
        type: "string"
        description: "The name of the project in the form of `projects/[PROJECT_ID]`,\
          \ under which\nthe occurrences are to be created."
      occurrences:
        type: "array"
        description: "The occurrences to create. Max allowed length is 1000."
        items:
          $ref: "#/definitions/v1beta1Occurrence"
    description: "Request to create occurrences in batch."
  v1beta1BatchCreateOccurrencesResponse:
    type: "object"
    properties:
      occurrences:
        type: "array"
        description: "The occurrences that were created."
        items:
          $ref: "#/definitions/v1beta1Occurrence"
    description: "Response for creating occurrences in batch."
    example:
      occurrences:
      - discovered:
          discovered:
            last_analysis_time: "2000-01-23T04:56:07.000+00:00"
            analysis_status: {}
            continuous_analysis: {}
            analysis_status_error:
              code: 1
              details:
              - value: "value"
                type_url: "type_url"
              - value: "value"
                type_url: "type_url"
              message: "message"
        attestation:
          attestation:
            pgp_signed_attestation:
              content_type: {}
              signature: "signature"
              pgp_key_id: "pgp_key_id"
            generic_signed_attestation:
              content_type: {}
              serialized_payload: "serialized_payload"
              signatures:
              - signature: "signature"
                public_key_id: "public_key_id"
              - signature: "signature"
                public_key_id: "public_key_id"
        create_time: "2000-01-23T04:56:07.000+00:00"
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        kind: {}
        intoto:
          link:
            environment:
              custom_values:
                key: "custom_values"
            effective_command:
            - "effective_command"
            - "effective_command"
            materials:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            byproducts:
              custom_values:
                key: "custom_values"
            products:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
          signatures:
          - key_id: "key_id"
            signature: "signature"
          - key_id: "key_id"
            signature: "signature"
        vulnerability:
          severity: {}
          short_description: "short_description"
          cvss_score: 0.8008282
          long_description: "long_description"
          related_urls:
          - label: "label"
            url: "url"
          - label: "label"
            url: "url"
          type: "type"
          package_issue:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
        remediation: "remediation"
        update_time: "2000-01-23T04:56:07.000+00:00"
        build:
          provenance:
            creator: "creator"
            create_time: "2000-01-23T04:56:07.000+00:00"
            trigger_id: "trigger_id"
            end_time: "2000-01-23T04:56:07.000+00:00"
            built_artifacts:
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            logs_uri: "logs_uri"
            builder_version: "builder_version"
            source_provenance:
              artifact_storage_source_uri: "artifact_storage_source_uri"
              additional_contexts:
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              file_hashes:
                key:
                  file_hash:
                  - type: {}
                    value: "value"
                  - type: {}
                    value: "value"
              context:
                git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
            start_time: "2000-01-23T04:56:07.000+00:00"
            project_id: "project_id"
            id: "id"
            commands:
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            build_options:
              key: "build_options"
          provenance_bytes: "provenance_bytes"
        installation:
          installation:
            name: "name"
            location:
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
        name: "name"
        derived_image:
          derived_image:
            distance: 6
            base_resource_url: "base_resource_url"
            fingerprint:
              v2_name: "v2_name"
              v1_name: "v1_name"
              v2_blob:
              - "v2_blob"
              - "v2_blob"
            layer_info:
            - arguments: "arguments"
              directive: {}
            - arguments: "arguments"
              directive: {}
        note_name: "note_name"
        deployment:
          deployment:
            user_email: "user_email"
            address: "address"
            resource_uri:
            - "resource_uri"
            - "resource_uri"
            undeploy_time: "2000-01-23T04:56:07.000+00:00"
            deploy_time: "2000-01-23T04:56:07.000+00:00"
            config: "config"
            platform: {}
      - discovered:
          discovered:
            last_analysis_time: "2000-01-23T04:56:07.000+00:00"
            analysis_status: {}
            continuous_analysis: {}
            analysis_status_error:
              code: 1
              details:
              - value: "value"
                type_url: "type_url"
              - value: "value"
                type_url: "type_url"
              message: "message"
        attestation:
          attestation:
            pgp_signed_attestation:
              content_type: {}
              signature: "signature"
              pgp_key_id: "pgp_key_id"
            generic_signed_attestation:
              content_type: {}
              serialized_payload: "serialized_payload"
              signatures:
              - signature: "signature"
                public_key_id: "public_key_id"
              - signature: "signature"
                public_key_id: "public_key_id"
        create_time: "2000-01-23T04:56:07.000+00:00"
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        kind: {}
        intoto:
          link:
            environment:
              custom_values:
                key: "custom_values"
            effective_command:
            - "effective_command"
            - "effective_command"
            materials:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            byproducts:
              custom_values:
                key: "custom_values"
            products:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
          signatures:
          - key_id: "key_id"
            signature: "signature"
          - key_id: "key_id"
            signature: "signature"
        vulnerability:
          severity: {}
          short_description: "short_description"
          cvss_score: 0.8008282
          long_description: "long_description"
          related_urls:
          - label: "label"
            url: "url"
          - label: "label"
            url: "url"
          type: "type"
          package_issue:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
        remediation: "remediation"
        update_time: "2000-01-23T04:56:07.000+00:00"
        build:
          provenance:
            creator: "creator"
            create_time: "2000-01-23T04:56:07.000+00:00"
            trigger_id: "trigger_id"
            end_time: "2000-01-23T04:56:07.000+00:00"
            built_artifacts:
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            logs_uri: "logs_uri"
            builder_version: "builder_version"
            source_provenance:
              artifact_storage_source_uri: "artifact_storage_source_uri"
              additional_contexts:
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              file_hashes:
                key:
                  file_hash:
                  - type: {}
                    value: "value"
                  - type: {}
                    value: "value"
              context:
                git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
            start_time: "2000-01-23T04:56:07.000+00:00"
            project_id: "project_id"
            id: "id"
            commands:
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            build_options:
              key: "build_options"
          provenance_bytes: "provenance_bytes"
        installation:
          installation:
            name: "name"
            location:
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
        name: "name"
        derived_image:
          derived_image:
            distance: 6
            base_resource_url: "base_resource_url"
            fingerprint:
              v2_name: "v2_name"
              v1_name: "v1_name"
              v2_blob:
              - "v2_blob"
              - "v2_blob"
            layer_info:
            - arguments: "arguments"
              directive: {}
            - arguments: "arguments"
              directive: {}
        note_name: "note_name"
        deployment:
          deployment:
            user_email: "user_email"
            address: "address"
            resource_uri:
            - "resource_uri"
            - "resource_uri"
            undeploy_time: "2000-01-23T04:56:07.000+00:00"
            deploy_time: "2000-01-23T04:56:07.000+00:00"
            config: "config"
            platform: {}
  v1beta1ListNoteOccurrencesResponse:
    type: "object"
    properties:
      occurrences:
        type: "array"
        description: "The occurrences attached to the specified note."
        items:
          $ref: "#/definitions/v1beta1Occurrence"
      next_page_token:
        type: "string"
        description: "Token to provide to skip to a particular spot in the list."
    description: "Response for listing occurrences for a note."
    example:
      occurrences:
      - discovered:
          discovered:
            last_analysis_time: "2000-01-23T04:56:07.000+00:00"
            analysis_status: {}
            continuous_analysis: {}
            analysis_status_error:
              code: 1
              details:
              - value: "value"
                type_url: "type_url"
              - value: "value"
                type_url: "type_url"
              message: "message"
        attestation:
          attestation:
            pgp_signed_attestation:
              content_type: {}
              signature: "signature"
              pgp_key_id: "pgp_key_id"
            generic_signed_attestation:
              content_type: {}
              serialized_payload: "serialized_payload"
              signatures:
              - signature: "signature"
                public_key_id: "public_key_id"
              - signature: "signature"
                public_key_id: "public_key_id"
        create_time: "2000-01-23T04:56:07.000+00:00"
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        kind: {}
        intoto:
          link:
            environment:
              custom_values:
                key: "custom_values"
            effective_command:
            - "effective_command"
            - "effective_command"
            materials:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            byproducts:
              custom_values:
                key: "custom_values"
            products:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
          signatures:
          - key_id: "key_id"
            signature: "signature"
          - key_id: "key_id"
            signature: "signature"
        vulnerability:
          severity: {}
          short_description: "short_description"
          cvss_score: 0.8008282
          long_description: "long_description"
          related_urls:
          - label: "label"
            url: "url"
          - label: "label"
            url: "url"
          type: "type"
          package_issue:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
        remediation: "remediation"
        update_time: "2000-01-23T04:56:07.000+00:00"
        build:
          provenance:
            creator: "creator"
            create_time: "2000-01-23T04:56:07.000+00:00"
            trigger_id: "trigger_id"
            end_time: "2000-01-23T04:56:07.000+00:00"
            built_artifacts:
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            logs_uri: "logs_uri"
            builder_version: "builder_version"
            source_provenance:
              artifact_storage_source_uri: "artifact_storage_source_uri"
              additional_contexts:
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              file_hashes:
                key:
                  file_hash:
                  - type: {}
                    value: "value"
                  - type: {}
                    value: "value"
              context:
                git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
            start_time: "2000-01-23T04:56:07.000+00:00"
            project_id: "project_id"
            id: "id"
            commands:
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            build_options:
              key: "build_options"
          provenance_bytes: "provenance_bytes"
        installation:
          installation:
            name: "name"
            location:
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
        name: "name"
        derived_image:
          derived_image:
            distance: 6
            base_resource_url: "base_resource_url"
            fingerprint:
              v2_name: "v2_name"
              v1_name: "v1_name"
              v2_blob:
              - "v2_blob"
              - "v2_blob"
            layer_info:
            - arguments: "arguments"
              directive: {}
            - arguments: "arguments"
              directive: {}
        note_name: "note_name"
        deployment:
          deployment:
            user_email: "user_email"
            address: "address"
            resource_uri:
            - "resource_uri"
            - "resource_uri"
            undeploy_time: "2000-01-23T04:56:07.000+00:00"
            deploy_time: "2000-01-23T04:56:07.000+00:00"
            config: "config"
            platform: {}
      - discovered:
          discovered:
            last_analysis_time: "2000-01-23T04:56:07.000+00:00"
            analysis_status: {}
            continuous_analysis: {}
            analysis_status_error:
              code: 1
              details:
              - value: "value"
                type_url: "type_url"
              - value: "value"
                type_url: "type_url"
              message: "message"
        attestation:
          attestation:
            pgp_signed_attestation:
              content_type: {}
              signature: "signature"
              pgp_key_id: "pgp_key_id"
            generic_signed_attestation:
              content_type: {}
              serialized_payload: "serialized_payload"
              signatures:
              - signature: "signature"
                public_key_id: "public_key_id"
              - signature: "signature"
                public_key_id: "public_key_id"
        create_time: "2000-01-23T04:56:07.000+00:00"
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        kind: {}
        intoto:
          link:
            environment:
              custom_values:
                key: "custom_values"
            effective_command:
            - "effective_command"
            - "effective_command"
            materials:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            byproducts:
              custom_values:
                key: "custom_values"
            products:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
          signatures:
          - key_id: "key_id"
            signature: "signature"
          - key_id: "key_id"
            signature: "signature"
        vulnerability:
          severity: {}
          short_description: "short_description"
          cvss_score: 0.8008282
          long_description: "long_description"
          related_urls:
          - label: "label"
            url: "url"
          - label: "label"
            url: "url"
          type: "type"
          package_issue:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
        remediation: "remediation"
        update_time: "2000-01-23T04:56:07.000+00:00"
        build:
          provenance:
            creator: "creator"
            create_time: "2000-01-23T04:56:07.000+00:00"
            trigger_id: "trigger_id"
            end_time: "2000-01-23T04:56:07.000+00:00"
            built_artifacts:
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            logs_uri: "logs_uri"
            builder_version: "builder_version"
            source_provenance:
              artifact_storage_source_uri: "artifact_storage_source_uri"
              additional_contexts:
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              file_hashes:
                key:
                  file_hash:
                  - type: {}
                    value: "value"
                  - type: {}
                    value: "value"
              context:
                git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
            start_time: "2000-01-23T04:56:07.000+00:00"
            project_id: "project_id"
            id: "id"
            commands:
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            build_options:
              key: "build_options"
          provenance_bytes: "provenance_bytes"
        installation:
          installation:
            name: "name"
            location:
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
        name: "name"
        derived_image:
          derived_image:
            distance: 6
            base_resource_url: "base_resource_url"
            fingerprint:
              v2_name: "v2_name"
              v1_name: "v1_name"
              v2_blob:
              - "v2_blob"
              - "v2_blob"
            layer_info:
            - arguments: "arguments"
              directive: {}
            - arguments: "arguments"
              directive: {}
        note_name: "note_name"
        deployment:
          deployment:
            user_email: "user_email"
            address: "address"
            resource_uri:
            - "resource_uri"
            - "resource_uri"
            undeploy_time: "2000-01-23T04:56:07.000+00:00"
            deploy_time: "2000-01-23T04:56:07.000+00:00"
            config: "config"
            platform: {}
      next_page_token: "next_page_token"
  v1beta1ListNotesResponse:
    type: "object"
    properties:
      notes:
        type: "array"
        description: "The notes requested."
        items:
          $ref: "#/definitions/v1beta1Note"
      next_page_token:
        type: "string"
        description: "The next pagination token in the list response. It should be\
          \ used as\n`page_token` for the following request. An empty value means\
          \ no more\nresults."
    description: "Response for listing notes."
    example:
      notes:
      - attestation_authority:
          hint:
            human_readable_name: "human_readable_name"
        short_description: "short_description"
        related_note_names:
        - "related_note_names"
        - "related_note_names"
        package:
          name: "name"
          distribution:
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
        create_time: "2000-01-23T04:56:07.000+00:00"
        kind: {}
        related_url:
        - label: "label"
          url: "url"
        - label: "label"
          url: "url"
        expiration_time: "2000-01-23T04:56:07.000+00:00"
        intoto:
          expected_products:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          step_name: "step_name"
          signing_keys:
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          expected_command:
          - "expected_command"
          - "expected_command"
          threshold: "threshold"
          expected_materials:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
        long_description: "long_description"
        vulnerability:
          severity: {}
          cvss_score: 0.8008282
          cvss_v3:
            attack_complexity: {}
            base_score: 1.4658129
            user_interaction: {}
            scope: {}
            impact_score: 5.637377
            confidentiality_impact: {}
            attack_vector: {}
            exploitability_score: 5.962134
            privileges_required: {}
          details:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          source_update_time: "2000-01-23T04:56:07.000+00:00"
          windows_details:
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
        update_time: "2000-01-23T04:56:07.000+00:00"
        base_image:
          resource_url: "resource_url"
          fingerprint:
            v2_name: "v2_name"
            v1_name: "v1_name"
            v2_blob:
            - "v2_blob"
            - "v2_blob"
        deployable:
          resource_uri:
          - "resource_uri"
          - "resource_uri"
        build:
          signature:
            public_key: "public_key"
            key_type: {}
            signature: "signature"
            key_id: "key_id"
          builder_version: "builder_version"
        discovery: {}
        name: "name"
      - attestation_authority:
          hint:
            human_readable_name: "human_readable_name"
        short_description: "short_description"
        related_note_names:
        - "related_note_names"
        - "related_note_names"
        package:
          name: "name"
          distribution:
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
          - latest_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            cpe_uri: "cpe_uri"
            description: "description"
            maintainer: "maintainer"
            url: "url"
            architecture: {}
        create_time: "2000-01-23T04:56:07.000+00:00"
        kind: {}
        related_url:
        - label: "label"
          url: "url"
        - label: "label"
          url: "url"
        expiration_time: "2000-01-23T04:56:07.000+00:00"
        intoto:
          expected_products:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          step_name: "step_name"
          signing_keys:
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          - key_type: "key_type"
            key_scheme: "key_scheme"
            key_id: "key_id"
            public_key_value: "public_key_value"
          expected_command:
          - "expected_command"
          - "expected_command"
          threshold: "threshold"
          expected_materials:
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
          - artifact_rule:
            - "artifact_rule"
            - "artifact_rule"
        long_description: "long_description"
        vulnerability:
          severity: {}
          cvss_score: 0.8008282
          cvss_v3:
            attack_complexity: {}
            base_score: 1.4658129
            user_interaction: {}
            scope: {}
            impact_score: 5.637377
            confidentiality_impact: {}
            attack_vector: {}
            exploitability_score: 5.962134
            privileges_required: {}
          details:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            package: "package"
            cpe_uri: "cpe_uri"
            max_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            description: "description"
            min_affected_version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
            severity_name: "severity_name"
            source_update_time: "2000-01-23T04:56:07.000+00:00"
            package_type: "package_type"
            is_obsolete: true
          source_update_time: "2000-01-23T04:56:07.000+00:00"
          windows_details:
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
          - cpe_uri: "cpe_uri"
            fixing_kbs:
            - name: "name"
              url: "url"
            - name: "name"
              url: "url"
            name: "name"
            description: "description"
        update_time: "2000-01-23T04:56:07.000+00:00"
        base_image:
          resource_url: "resource_url"
          fingerprint:
            v2_name: "v2_name"
            v1_name: "v1_name"
            v2_blob:
            - "v2_blob"
            - "v2_blob"
        deployable:
          resource_uri:
          - "resource_uri"
          - "resource_uri"
        build:
          signature:
            public_key: "public_key"
            key_type: {}
            signature: "signature"
            key_id: "key_id"
          builder_version: "builder_version"
        discovery: {}
        name: "name"
      next_page_token: "next_page_token"
  v1beta1ListOccurrencesResponse:
    type: "object"
    properties:
      occurrences:
        type: "array"
        description: "The occurrences requested."
        items:
          $ref: "#/definitions/v1beta1Occurrence"
      next_page_token:
        type: "string"
        description: "The next pagination token in the list response. It should be\
          \ used as\n`page_token` for the following request. An empty value means\
          \ no more\nresults."
    description: "Response for listing occurrences."
    example:
      occurrences:
      - discovered:
          discovered:
            last_analysis_time: "2000-01-23T04:56:07.000+00:00"
            analysis_status: {}
            continuous_analysis: {}
            analysis_status_error:
              code: 1
              details:
              - value: "value"
                type_url: "type_url"
              - value: "value"
                type_url: "type_url"
              message: "message"
        attestation:
          attestation:
            pgp_signed_attestation:
              content_type: {}
              signature: "signature"
              pgp_key_id: "pgp_key_id"
            generic_signed_attestation:
              content_type: {}
              serialized_payload: "serialized_payload"
              signatures:
              - signature: "signature"
                public_key_id: "public_key_id"
              - signature: "signature"
                public_key_id: "public_key_id"
        create_time: "2000-01-23T04:56:07.000+00:00"
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        kind: {}
        intoto:
          link:
            environment:
              custom_values:
                key: "custom_values"
            effective_command:
            - "effective_command"
            - "effective_command"
            materials:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            byproducts:
              custom_values:
                key: "custom_values"
            products:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
          signatures:
          - key_id: "key_id"
            signature: "signature"
          - key_id: "key_id"
            signature: "signature"
        vulnerability:
          severity: {}
          short_description: "short_description"
          cvss_score: 0.8008282
          long_description: "long_description"
          related_urls:
          - label: "label"
            url: "url"
          - label: "label"
            url: "url"
          type: "type"
          package_issue:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
        remediation: "remediation"
        update_time: "2000-01-23T04:56:07.000+00:00"
        build:
          provenance:
            creator: "creator"
            create_time: "2000-01-23T04:56:07.000+00:00"
            trigger_id: "trigger_id"
            end_time: "2000-01-23T04:56:07.000+00:00"
            built_artifacts:
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            logs_uri: "logs_uri"
            builder_version: "builder_version"
            source_provenance:
              artifact_storage_source_uri: "artifact_storage_source_uri"
              additional_contexts:
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              file_hashes:
                key:
                  file_hash:
                  - type: {}
                    value: "value"
                  - type: {}
                    value: "value"
              context:
                git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
            start_time: "2000-01-23T04:56:07.000+00:00"
            project_id: "project_id"
            id: "id"
            commands:
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            build_options:
              key: "build_options"
          provenance_bytes: "provenance_bytes"
        installation:
          installation:
            name: "name"
            location:
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
        name: "name"
        derived_image:
          derived_image:
            distance: 6
            base_resource_url: "base_resource_url"
            fingerprint:
              v2_name: "v2_name"
              v1_name: "v1_name"
              v2_blob:
              - "v2_blob"
              - "v2_blob"
            layer_info:
            - arguments: "arguments"
              directive: {}
            - arguments: "arguments"
              directive: {}
        note_name: "note_name"
        deployment:
          deployment:
            user_email: "user_email"
            address: "address"
            resource_uri:
            - "resource_uri"
            - "resource_uri"
            undeploy_time: "2000-01-23T04:56:07.000+00:00"
            deploy_time: "2000-01-23T04:56:07.000+00:00"
            config: "config"
            platform: {}
      - discovered:
          discovered:
            last_analysis_time: "2000-01-23T04:56:07.000+00:00"
            analysis_status: {}
            continuous_analysis: {}
            analysis_status_error:
              code: 1
              details:
              - value: "value"
                type_url: "type_url"
              - value: "value"
                type_url: "type_url"
              message: "message"
        attestation:
          attestation:
            pgp_signed_attestation:
              content_type: {}
              signature: "signature"
              pgp_key_id: "pgp_key_id"
            generic_signed_attestation:
              content_type: {}
              serialized_payload: "serialized_payload"
              signatures:
              - signature: "signature"
                public_key_id: "public_key_id"
              - signature: "signature"
                public_key_id: "public_key_id"
        create_time: "2000-01-23T04:56:07.000+00:00"
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        kind: {}
        intoto:
          link:
            environment:
              custom_values:
                key: "custom_values"
            effective_command:
            - "effective_command"
            - "effective_command"
            materials:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            byproducts:
              custom_values:
                key: "custom_values"
            products:
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
            - resource_uri: "resource_uri"
              hashes:
                sha256: "sha256"
          signatures:
          - key_id: "key_id"
            signature: "signature"
          - key_id: "key_id"
            signature: "signature"
        vulnerability:
          severity: {}
          short_description: "short_description"
          cvss_score: 0.8008282
          long_description: "long_description"
          related_urls:
          - label: "label"
            url: "url"
          - label: "label"
            url: "url"
          type: "type"
          package_issue:
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
          - fixed_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            affected_location:
              package: "package"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            severity_name: "severity_name"
        remediation: "remediation"
        update_time: "2000-01-23T04:56:07.000+00:00"
        build:
          provenance:
            creator: "creator"
            create_time: "2000-01-23T04:56:07.000+00:00"
            trigger_id: "trigger_id"
            end_time: "2000-01-23T04:56:07.000+00:00"
            built_artifacts:
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            - names:
              - "names"
              - "names"
              checksum: "checksum"
              id: "id"
            logs_uri: "logs_uri"
            builder_version: "builder_version"
            source_provenance:
              artifact_storage_source_uri: "artifact_storage_source_uri"
              additional_contexts:
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              - git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
              file_hashes:
                key:
                  file_hash:
                  - type: {}
                    value: "value"
                  - type: {}
                    value: "value"
              context:
                git:
                  url: "url"
                  revision_id: "revision_id"
                cloud_repo:
                  repo_id:
                    uid: "uid"
                    project_repo_id:
                      project_id: "project_id"
                      repo_name: "repo_name"
                  alias_context:
                    kind: {}
                    name: "name"
                  revision_id: "revision_id"
                gerrit:
                  gerrit_project: "gerrit_project"
                  alias_context:
                    kind: {}
                    name: "name"
                  host_uri: "host_uri"
                  revision_id: "revision_id"
                labels:
                  key: "labels"
            start_time: "2000-01-23T04:56:07.000+00:00"
            project_id: "project_id"
            id: "id"
            commands:
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            - args:
              - "args"
              - "args"
              name: "name"
              id: "id"
              wait_for:
              - "wait_for"
              - "wait_for"
              env:
              - "env"
              - "env"
              dir: "dir"
            build_options:
              key: "build_options"
          provenance_bytes: "provenance_bytes"
        installation:
          installation:
            name: "name"
            location:
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
            - path: "path"
              cpe_uri: "cpe_uri"
              version:
                kind: {}
                name: "name"
                epoch: 6
                revision: "revision"
        name: "name"
        derived_image:
          derived_image:
            distance: 6
            base_resource_url: "base_resource_url"
            fingerprint:
              v2_name: "v2_name"
              v1_name: "v1_name"
              v2_blob:
              - "v2_blob"
              - "v2_blob"
            layer_info:
            - arguments: "arguments"
              directive: {}
            - arguments: "arguments"
              directive: {}
        note_name: "note_name"
        deployment:
          deployment:
            user_email: "user_email"
            address: "address"
            resource_uri:
            - "resource_uri"
            - "resource_uri"
            undeploy_time: "2000-01-23T04:56:07.000+00:00"
            deploy_time: "2000-01-23T04:56:07.000+00:00"
            config: "config"
            platform: {}
      next_page_token: "next_page_token"
  v1beta1Note:
    type: "object"
    properties:
      name:
        type: "string"
        description: "Output only. The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`."
        readOnly: true
      short_description:
        type: "string"
        description: "A one sentence description of this note."
      long_description:
        type: "string"
        description: "A detailed description of this note."
      kind:
        description: "Output only. The type of analysis. This field can be used as\
          \ a filter in\nlist requests."
        readOnly: true
        $ref: "#/definitions/v1beta1NoteKind"
      related_url:
        type: "array"
        description: "URLs associated with this note."
        items:
          $ref: "#/definitions/v1beta1RelatedUrl"
      expiration_time:
        type: "string"
        format: "date-time"
        description: "Time of expiration for this note. Empty if note does not expire."
      create_time:
        type: "string"
        format: "date-time"
        description: "Output only. The time this note was created. This field can\
          \ be used as a\nfilter in list requests."
        readOnly: true
      update_time:
        type: "string"
        format: "date-time"
        description: "Output only. The time this note was last updated. This field\
          \ can be used as\na filter in list requests."
        readOnly: true
      related_note_names:
        type: "array"
        description: "Other notes related to this note."
        items:
          type: "string"
      vulnerability:
        description: "A note describing a package vulnerability."
        $ref: "#/definitions/vulnerabilityVulnerability"
      build:
        description: "A note describing build provenance for a verifiable build."
        $ref: "#/definitions/buildBuild"
      base_image:
        description: "A note describing a base image."
        $ref: "#/definitions/imageBasis"
      package:
        description: "A note describing a package hosted by various package managers."
        $ref: "#/definitions/packagePackage"
      deployable:
        description: "A note describing something that can be deployed."
        $ref: "#/definitions/deploymentDeployable"
      discovery:
        description: "A note describing the initial analysis of a resource."
        $ref: "#/definitions/discoveryDiscovery"
      attestation_authority:
        description: "A note describing an attestation role."
        $ref: "#/definitions/attestationAuthority"
      intoto:
        description: "A note describing an in-toto link."
        $ref: "#/definitions/intotoInToto"
    description: "A type of analysis that can be done for a resource."
    example:
      attestation_authority:
        hint:
          human_readable_name: "human_readable_name"
      short_description: "short_description"
      related_note_names:
      - "related_note_names"
      - "related_note_names"
      package:
        name: "name"
        distribution:
        - latest_version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
          cpe_uri: "cpe_uri"
          description: "description"
          maintainer: "maintainer"
          url: "url"
          architecture: {}
        - latest_version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
          cpe_uri: "cpe_uri"
          description: "description"
          maintainer: "maintainer"
          url: "url"
          architecture: {}
      create_time: "2000-01-23T04:56:07.000+00:00"
      kind: {}
      related_url:
      - label: "label"
        url: "url"
      - label: "label"
        url: "url"
      expiration_time: "2000-01-23T04:56:07.000+00:00"
      intoto:
        expected_products:
        - artifact_rule:
          - "artifact_rule"
          - "artifact_rule"
        - artifact_rule:
          - "artifact_rule"
          - "artifact_rule"
        step_name: "step_name"
        signing_keys:
        - key_type: "key_type"
          key_scheme: "key_scheme"
          key_id: "key_id"
          public_key_value: "public_key_value"
        - key_type: "key_type"
          key_scheme: "key_scheme"
          key_id: "key_id"
          public_key_value: "public_key_value"
        expected_command:
        - "expected_command"
        - "expected_command"
        threshold: "threshold"
        expected_materials:
        - artifact_rule:
          - "artifact_rule"
          - "artifact_rule"
        - artifact_rule:
          - "artifact_rule"
          - "artifact_rule"
      long_description: "long_description"
      vulnerability:
        severity: {}
        cvss_score: 0.8008282
        cvss_v3:
          attack_complexity: {}
          base_score: 1.4658129
          user_interaction: {}
          scope: {}
          impact_score: 5.637377
          confidentiality_impact: {}
          attack_vector: {}
          exploitability_score: 5.962134
          privileges_required: {}
        details:
        - fixed_location:
            package: "package"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
          package: "package"
          cpe_uri: "cpe_uri"
          max_affected_version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
          description: "description"
          min_affected_version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
          severity_name: "severity_name"
          source_update_time: "2000-01-23T04:56:07.000+00:00"
          package_type: "package_type"
          is_obsolete: true
        - fixed_location:
            package: "package"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
          package: "package"
          cpe_uri: "cpe_uri"
          max_affected_version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
          description: "description"
          min_affected_version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
          severity_name: "severity_name"
          source_update_time: "2000-01-23T04:56:07.000+00:00"
          package_type: "package_type"
          is_obsolete: true
        source_update_time: "2000-01-23T04:56:07.000+00:00"
        windows_details:
        - cpe_uri: "cpe_uri"
          fixing_kbs:
          - name: "name"
            url: "url"
          - name: "name"
            url: "url"
          name: "name"
          description: "description"
        - cpe_uri: "cpe_uri"
          fixing_kbs:
          - name: "name"
            url: "url"
          - name: "name"
            url: "url"
          name: "name"
          description: "description"
      update_time: "2000-01-23T04:56:07.000+00:00"
      base_image:
        resource_url: "resource_url"
        fingerprint:
          v2_name: "v2_name"
          v1_name: "v1_name"
          v2_blob:
          - "v2_blob"
          - "v2_blob"
      deployable:
        resource_uri:
        - "resource_uri"
        - "resource_uri"
      build:
        signature:
          public_key: "public_key"
          key_type: {}
          signature: "signature"
          key_id: "key_id"
        builder_version: "builder_version"
      discovery: {}
      name: "name"
  v1beta1NoteKind:
    type: "string"
    description: "Kind represents the kinds of notes supported.\n\n - NOTE_KIND_UNSPECIFIED:\
      \ Unknown.\n - VULNERABILITY: The note and occurrence represent a package vulnerability.\n\
      \ - BUILD: The note and occurrence assert build provenance.\n - IMAGE: This\
      \ represents an image basis relationship.\n - PACKAGE: This represents a package\
      \ installed via a package manager.\n - DEPLOYMENT: The note and occurrence track\
      \ deployment events.\n - DISCOVERY: The note and occurrence track the initial\
      \ discovery status of a resource.\n - ATTESTATION: This represents a logical\
      \ \"role\" that can attest to artifacts.\n - INTOTO: This represents an in-toto\
      \ link."
    enum:
    - "NOTE_KIND_UNSPECIFIED"
    - "VULNERABILITY"
    - "BUILD"
    - "IMAGE"
    - "PACKAGE"
    - "DEPLOYMENT"
    - "DISCOVERY"
    - "ATTESTATION"
    - "INTOTO"
    default: "NOTE_KIND_UNSPECIFIED"
  v1beta1Occurrence:
    type: "object"
    properties:
      name:
        type: "string"
        description: "Output only. The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`."
        readOnly: true
      resource:
        description: "Required. Immutable. The resource for which the occurrence applies."
        $ref: "#/definitions/v1beta1Resource"
      note_name:
        type: "string"
        description: "Required. Immutable. The analysis note associated with this\
          \ occurrence, in\nthe form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.\
          \ This field can be\nused as a filter in list requests."
      kind:
        description: "Output only. This explicitly denotes which of the occurrence\
          \ details are\nspecified. This field can be used as a filter in list requests."
        readOnly: true
        $ref: "#/definitions/v1beta1NoteKind"
      remediation:
        type: "string"
        description: "A description of actions that can be taken to remedy the note."
      create_time:
        type: "string"
        format: "date-time"
        description: "Output only. The time this occurrence was created."
        readOnly: true
      update_time:
        type: "string"
        format: "date-time"
        description: "Output only. The time this occurrence was last updated."
        readOnly: true
      vulnerability:
        description: "Describes a security vulnerability."
        $ref: "#/definitions/v1beta1vulnerabilityDetails"
      build:
        description: "Describes a verifiable build."
        $ref: "#/definitions/v1beta1buildDetails"
      derived_image:
        description: "Describes how this resource derives from the basis in the associated\n\
          note."
        $ref: "#/definitions/v1beta1imageDetails"
      installation:
        description: "Describes the installation of a package on the linked resource."
        $ref: "#/definitions/v1beta1packageDetails"
      deployment:
        description: "Describes the deployment of an artifact on a runtime."
        $ref: "#/definitions/v1beta1deploymentDetails"
      discovered:
        description: "Describes when a resource was discovered."
        $ref: "#/definitions/v1beta1discoveryDetails"
      attestation:
        description: "Describes an attestation of an artifact."
        $ref: "#/definitions/v1beta1attestationDetails"
      intoto:
        description: "Describes a specific in-toto link."
        $ref: "#/definitions/v1beta1intotoDetails"
    description: "An instance of an analysis type that has been found on a resource."
    example:
      discovered:
        discovered:
          last_analysis_time: "2000-01-23T04:56:07.000+00:00"
          analysis_status: {}
          continuous_analysis: {}
          analysis_status_error:
            code: 1
            details:
            - value: "value"
              type_url: "type_url"
            - value: "value"
              type_url: "type_url"
            message: "message"
      attestation:
        attestation:
          pgp_signed_attestation:
            content_type: {}
            signature: "signature"
            pgp_key_id: "pgp_key_id"
          generic_signed_attestation:
            content_type: {}
            serialized_payload: "serialized_payload"
            signatures:
            - signature: "signature"
              public_key_id: "public_key_id"
            - signature: "signature"
              public_key_id: "public_key_id"
      create_time: "2000-01-23T04:56:07.000+00:00"
      resource:
        name: "name"
        content_hash:
          type: {}
          value: "value"
        uri: "uri"
      kind: {}
      intoto:
        link:
          environment:
            custom_values:
              key: "custom_values"
          effective_command:
          - "effective_command"
          - "effective_command"
          materials:
          - resource_uri: "resource_uri"
            hashes:
              sha256: "sha256"
          - resource_uri: "resource_uri"
            hashes:
              sha256: "sha256"
          byproducts:
            custom_values:
              key: "custom_values"
          products:
          - resource_uri: "resource_uri"
            hashes:
              sha256: "sha256"
          - resource_uri: "resource_uri"
            hashes:
              sha256: "sha256"
        signatures:
        - key_id: "key_id"
          signature: "signature"
        - key_id: "key_id"
          signature: "signature"
      vulnerability:
        severity: {}
        short_description: "short_description"
        cvss_score: 0.8008282
        long_description: "long_description"
        related_urls:
        - label: "label"
          url: "url"
        - label: "label"
          url: "url"
        type: "type"
        package_issue:
        - fixed_location:
            package: "package"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
          affected_location:
            package: "package"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
          severity_name: "severity_name"
        - fixed_location:
            package: "package"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
          affected_location:
            package: "package"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
          severity_name: "severity_name"
      remediation: "remediation"
      update_time: "2000-01-23T04:56:07.000+00:00"
      build:
        provenance:
          creator: "creator"
          create_time: "2000-01-23T04:56:07.000+00:00"
          trigger_id: "trigger_id"
          end_time: "2000-01-23T04:56:07.000+00:00"
          built_artifacts:
          - names:
            - "names"
            - "names"
            checksum: "checksum"
            id: "id"
          - names:
            - "names"
            - "names"
            checksum: "checksum"
            id: "id"
          logs_uri: "logs_uri"
          builder_version: "builder_version"
          source_provenance:
            artifact_storage_source_uri: "artifact_storage_source_uri"
            additional_contexts:
            - git:
                url: "url"
                revision_id: "revision_id"
              cloud_repo:
                repo_id:
                  uid: "uid"
                  project_repo_id:
                    project_id: "project_id"
                    repo_name: "repo_name"
                alias_context:
                  kind: {}
                  name: "name"
                revision_id: "revision_id"
              gerrit:
                gerrit_project: "gerrit_project"
                alias_context:
                  kind: {}
                  name: "name"
                host_uri: "host_uri"
                revision_id: "revision_id"
              labels:
                key: "labels"
            - git:
                url: "url"
                revision_id: "revision_id"
              cloud_repo:
                repo_id:
                  uid: "uid"
                  project_repo_id:
                    project_id: "project_id"
                    repo_name: "repo_name"
                alias_context:
                  kind: {}
                  name: "name"
                revision_id: "revision_id"
              gerrit:
                gerrit_project: "gerrit_project"
                alias_context:
                  kind: {}
                  name: "name"
                host_uri: "host_uri"
                revision_id: "revision_id"
              labels:
                key: "labels"
            file_hashes:
              key:
                file_hash:
                - type: {}
                  value: "value"
                - type: {}
                  value: "value"
            context:
              git:
                url: "url"
                revision_id: "revision_id"
              cloud_repo:
                repo_id:
                  uid: "uid"
                  project_repo_id:
                    project_id: "project_id"
                    repo_name: "repo_name"
                alias_context:
                  kind: {}
                  name: "name"
                revision_id: "revision_id"
              gerrit:
                gerrit_project: "gerrit_project"
                alias_context:
                  kind: {}
                  name: "name"
                host_uri: "host_uri"
                revision_id: "revision_id"
              labels:
                key: "labels"
          start_time: "2000-01-23T04:56:07.000+00:00"
          project_id: "project_id"
          id: "id"
          commands:
          - args:
            - "args"
            - "args"
            name: "name"
            id: "id"
            wait_for:
            - "wait_for"
            - "wait_for"
            env:
            - "env"
            - "env"
            dir: "dir"
          - args:
            - "args"
            - "args"
            name: "name"
            id: "id"
            wait_for:
            - "wait_for"
            - "wait_for"
            env:
            - "env"
            - "env"
            dir: "dir"
          build_options:
            key: "build_options"
        provenance_bytes: "provenance_bytes"
      installation:
        installation:
          name: "name"
          location:
          - path: "path"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
          - path: "path"
            cpe_uri: "cpe_uri"
            version:
              kind: {}
              name: "name"
              epoch: 6
              revision: "revision"
      name: "name"
      derived_image:
        derived_image:
          distance: 6
          base_resource_url: "base_resource_url"
          fingerprint:
            v2_name: "v2_name"
            v1_name: "v1_name"
            v2_blob:
            - "v2_blob"
            - "v2_blob"
          layer_info:
          - arguments: "arguments"
            directive: {}
          - arguments: "arguments"
            directive: {}
      note_name: "note_name"
      deployment:
        deployment:
          user_email: "user_email"
          address: "address"
          resource_uri:
          - "resource_uri"
          - "resource_uri"
          undeploy_time: "2000-01-23T04:56:07.000+00:00"
          deploy_time: "2000-01-23T04:56:07.000+00:00"
          config: "config"
          platform: {}
  v1beta1RelatedUrl:
    type: "object"
    properties:
      url:
        type: "string"
        description: "Specific URL associated with the resource."
      label:
        type: "string"
        description: "Label to describe usage of the URL."
    description: "Metadata for any related URL information."
    example:
      label: "label"
      url: "url"
  v1beta1Resource:
    type: "object"
    properties:
      name:
        type: "string"
        description: "Deprecated, do not use. Use uri instead.\n\nThe name of the\
          \ resource. For example, the name of a Docker image -\n\"Debian\"."
      uri:
        type: "string"
        description: "Required. The unique URI of the resource. For example,\n`https://gcr.io/project/image@sha256:foo`\
          \ for a Docker image."
      content_hash:
        description: "Deprecated, do not use. Use uri instead.\n\nThe hash of the\
          \ resource content. For example, the Docker digest."
        $ref: "#/definitions/provenanceHash"
    description: "An entity that can have metadata. For example, a Docker image."
    example:
      name: "name"
      content_hash:
        type: {}
        value: "value"
      uri: "uri"
  v1beta1VulnerabilityOccurrencesSummary:
    type: "object"
    properties:
      counts:
        type: "array"
        description: "A listing by resource of the number of fixable and total vulnerabilities."
        items:
          $ref: "#/definitions/VulnerabilityOccurrencesSummaryFixableTotalByDigest"
    description: "A summary of how many vulnerability occurrences there are per resource\
      \ and\nseverity type."
    example:
      counts:
      - severity: {}
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        total_count: "total_count"
        fixable_count: "fixable_count"
      - severity: {}
        resource:
          name: "name"
          content_hash:
            type: {}
            value: "value"
          uri: "uri"
        total_count: "total_count"
        fixable_count: "fixable_count"
  v1beta1attestationDetails:
    type: "object"
    properties:
      attestation:
        description: "Required. Attestation for the resource."
        $ref: "#/definitions/attestationAttestation"
    description: "Details of an attestation occurrence."
    example:
      attestation:
        pgp_signed_attestation:
          content_type: {}
          signature: "signature"
          pgp_key_id: "pgp_key_id"
        generic_signed_attestation:
          content_type: {}
          serialized_payload: "serialized_payload"
          signatures:
          - signature: "signature"
            public_key_id: "public_key_id"
          - signature: "signature"
            public_key_id: "public_key_id"
  v1beta1buildDetails:
    type: "object"
    properties:
      provenance:
        description: "Required. The actual provenance for the build."
        $ref: "#/definitions/provenanceBuildProvenance"
      provenance_bytes:
        type: "string"
        description: "Serialized JSON representation of the provenance, used in generating\
          \ the\nbuild signature in the corresponding build note. After verifying\
          \ the\nsignature, `provenance_bytes` can be unmarshalled and compared to\
          \ the\nprovenance to confirm that it is unchanged. A base64-encoded string\n\
          representation of the provenance bytes is used for the signature in order\n\
          to interoperate with openssl which expects this format for signature\nverification.\n\
          \nThe serialized form is captured both to avoid ambiguity in how the\nprovenance\
          \ is marshalled to json as well to prevent incompatibilities with\nfuture\
          \ changes."
    description: "Details of a build occurrence."
    example:
      provenance:
        creator: "creator"
        create_time: "2000-01-23T04:56:07.000+00:00"
        trigger_id: "trigger_id"
        end_time: "2000-01-23T04:56:07.000+00:00"
        built_artifacts:
        - names:
          - "names"
          - "names"
          checksum: "checksum"
          id: "id"
        - names:
          - "names"
          - "names"
          checksum: "checksum"
          id: "id"
        logs_uri: "logs_uri"
        builder_version: "builder_version"
        source_provenance:
          artifact_storage_source_uri: "artifact_storage_source_uri"
          additional_contexts:
          - git:
              url: "url"
              revision_id: "revision_id"
            cloud_repo:
              repo_id:
                uid: "uid"
                project_repo_id:
                  project_id: "project_id"
                  repo_name: "repo_name"
              alias_context:
                kind: {}
                name: "name"
              revision_id: "revision_id"
            gerrit:
              gerrit_project: "gerrit_project"
              alias_context:
                kind: {}
                name: "name"
              host_uri: "host_uri"
              revision_id: "revision_id"
            labels:
              key: "labels"
          - git:
              url: "url"
              revision_id: "revision_id"
            cloud_repo:
              repo_id:
                uid: "uid"
                project_repo_id:
                  project_id: "project_id"
                  repo_name: "repo_name"
              alias_context:
                kind: {}
                name: "name"
              revision_id: "revision_id"
            gerrit:
              gerrit_project: "gerrit_project"
              alias_context:
                kind: {}
                name: "name"
              host_uri: "host_uri"
              revision_id: "revision_id"
            labels:
              key: "labels"
          file_hashes:
            key:
              file_hash:
              - type: {}
                value: "value"
              - type: {}
                value: "value"
          context:
            git:
              url: "url"
              revision_id: "revision_id"
            cloud_repo:
              repo_id:
                uid: "uid"
                project_repo_id:
                  project_id: "project_id"
                  repo_name: "repo_name"
              alias_context:
                kind: {}
                name: "name"
              revision_id: "revision_id"
            gerrit:
              gerrit_project: "gerrit_project"
              alias_context:
                kind: {}
                name: "name"
              host_uri: "host_uri"
              revision_id: "revision_id"
            labels:
              key: "labels"
        start_time: "2000-01-23T04:56:07.000+00:00"
        project_id: "project_id"
        id: "id"
        commands:
        - args:
          - "args"
          - "args"
          name: "name"
          id: "id"
          wait_for:
          - "wait_for"
          - "wait_for"
          env:
          - "env"
          - "env"
          dir: "dir"
        - args:
          - "args"
          - "args"
          name: "name"
          id: "id"
          wait_for:
          - "wait_for"
          - "wait_for"
          env:
          - "env"
          - "env"
          dir: "dir"
        build_options:
          key: "build_options"
      provenance_bytes: "provenance_bytes"
  v1beta1deploymentDetails:
    type: "object"
    properties:
      deployment:
        description: "Required. Deployment history for the resource."
        $ref: "#/definitions/deploymentDeployment"
    description: "Details of a deployment occurrence."
    example:
      deployment:
        user_email: "user_email"
        address: "address"
        resource_uri:
        - "resource_uri"
        - "resource_uri"
        undeploy_time: "2000-01-23T04:56:07.000+00:00"
        deploy_time: "2000-01-23T04:56:07.000+00:00"
        config: "config"
        platform: {}
  v1beta1discoveryDetails:
    type: "object"
    properties:
      discovered:
        description: "Required. Analysis status for the discovered resource."
        $ref: "#/definitions/discoveryDiscovered"
    description: "Details of a discovery occurrence."
    example:
      discovered:
        last_analysis_time: "2000-01-23T04:56:07.000+00:00"
        analysis_status: {}
        continuous_analysis: {}
        analysis_status_error:
          code: 1
          details:
          - value: "value"
            type_url: "type_url"
          - value: "value"
            type_url: "type_url"
          message: "message"
  v1beta1imageDetails:
    type: "object"
    properties:
      derived_image:
        description: "Required. Immutable. The child image derived from the base image."
        $ref: "#/definitions/imageDerived"
    description: "Details of an image occurrence."
    example:
      derived_image:
        distance: 6
        base_resource_url: "base_resource_url"
        fingerprint:
          v2_name: "v2_name"
          v1_name: "v1_name"
          v2_blob:
          - "v2_blob"
          - "v2_blob"
        layer_info:
        - arguments: "arguments"
          directive: {}
        - arguments: "arguments"
          directive: {}
  v1beta1intotoDetails:
    type: "object"
    properties:
      signatures:
        type: "array"
        items:
          $ref: "#/definitions/v1beta1intotoSignature"
      link:
        $ref: "#/definitions/intotoLink"
    description: "This corresponds to a signed in-toto link - it is made up of one\
      \ or more\nsignatures and the in-toto link itself. This is used for occurrences\
      \ of a\nGrafeas in-toto note."
    example:
      link:
        environment:
          custom_values:
            key: "custom_values"
        effective_command:
        - "effective_command"
        - "effective_command"
        materials:
        - resource_uri: "resource_uri"
          hashes:
            sha256: "sha256"
        - resource_uri: "resource_uri"
          hashes:
            sha256: "sha256"
        byproducts:
          custom_values:
            key: "custom_values"
        products:
        - resource_uri: "resource_uri"
          hashes:
            sha256: "sha256"
        - resource_uri: "resource_uri"
          hashes:
            sha256: "sha256"
      signatures:
      - key_id: "key_id"
        signature: "signature"
      - key_id: "key_id"
        signature: "signature"
  v1beta1intotoSignature:
    type: "object"
    properties:
      key_id:
        type: "string"
      signature:
        type: "string"
    description: "A signature object consists of the KeyID used and the signature\
      \ itself."
    example:
      key_id: "key_id"
      signature: "signature"
  v1beta1packageDetails:
    type: "object"
    properties:
      installation:
        description: "Required. Where the package was installed."
        $ref: "#/definitions/packageInstallation"
    description: "Details of a package occurrence."
    example:
      installation:
        name: "name"
        location:
        - path: "path"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
        - path: "path"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
  v1beta1packageLocation:
    type: "object"
    properties:
      cpe_uri:
        type: "string"
        description: "Required. The CPE URI in [CPE format](https://cpe.mitre.org/specification/)\n\
          denoting the package manager version distributing a package."
      version:
        description: "The version installed at this location."
        $ref: "#/definitions/packageVersion"
      path:
        type: "string"
        description: "The path from which we gathered that this package/version is\
          \ installed."
    description: "An occurrence of a particular package installation found within\
      \ a system's\nfilesystem. E.g., glibc was found in `/var/lib/dpkg/status`."
    example:
      path: "path"
      cpe_uri: "cpe_uri"
      version:
        kind: {}
        name: "name"
        epoch: 6
        revision: "revision"
  v1beta1provenanceArtifact:
    type: "object"
    properties:
      checksum:
        type: "string"
        description: "Hash or checksum value of a binary, or Docker Registry 2.0 digest\
          \ of a\ncontainer."
      id:
        type: "string"
        description: "Artifact ID, if any; for container images, this will be a URL\
          \ by digest\nlike `gcr.io/projectID/imagename@sha256:123456`."
      names:
        type: "array"
        description: "Related artifact names. This may be the path to a binary or\
          \ jar file, or in\nthe case of a container build, the name used to push\
          \ the container image to\nGoogle Container Registry, as presented to `docker\
          \ push`. Note that a\nsingle Artifact ID can have multiple names, for example\
          \ if two tags are\napplied to one image."
        items:
          type: "string"
    description: "Artifact describes a build product."
    example:
      names:
      - "names"
      - "names"
      checksum: "checksum"
      id: "id"
  v1beta1vulnerabilityDetails:
    type: "object"
    properties:
      type:
        type: "string"
        title: "The type of package; whether native or non native(ruby gems, node.js\n\
          packages etc)"
      severity:
        description: "Output only. The note provider assigned Severity of the vulnerability."
        readOnly: true
        $ref: "#/definitions/vulnerabilitySeverity"
      cvss_score:
        type: "number"
        format: "float"
        description: "Output only. The CVSS score of this vulnerability. CVSS score\
          \ is on a\nscale of 0-10 where 0 indicates low severity and 10 indicates\
          \ high\nseverity."
        readOnly: true
      package_issue:
        type: "array"
        description: "Required. The set of affected locations and their fixes (if\
          \ available)\nwithin the associated resource."
        items:
          $ref: "#/definitions/vulnerabilityPackageIssue"
      short_description:
        type: "string"
        description: "Output only. A one sentence description of this vulnerability."
        readOnly: true
      long_description:
        type: "string"
        description: "Output only. A detailed description of this vulnerability."
        readOnly: true
      related_urls:
        type: "array"
        description: "Output only. URLs related to this vulnerability."
        readOnly: true
        items:
          $ref: "#/definitions/v1beta1RelatedUrl"
      effective_severity:
        description: "The distro assigned severity for this vulnerability when it\
          \ is\navailable, and note provider assigned severity when distro has not\
          \ yet\nassigned a severity for this vulnerability."
        $ref: "#/definitions/vulnerabilitySeverity"
    description: "Details of a vulnerability Occurrence."
    example:
      severity: {}
      short_description: "short_description"
      cvss_score: 0.8008282
      long_description: "long_description"
      related_urls:
      - label: "label"
        url: "url"
      - label: "label"
        url: "url"
      type: "type"
      package_issue:
      - fixed_location:
          package: "package"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
        affected_location:
          package: "package"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
        severity_name: "severity_name"
      - fixed_location:
          package: "package"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
        affected_location:
          package: "package"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
        severity_name: "severity_name"
  vulnerabilityCVSSv3:
    type: "object"
    properties:
      base_score:
        type: "number"
        format: "float"
        description: "The base score is a function of the base metric scores."
      exploitability_score:
        type: "number"
        format: "float"
      impact_score:
        type: "number"
        format: "float"
      attack_vector:
        description: "Base Metrics\nRepresents the intrinsic characteristics of a\
          \ vulnerability that are\nconstant over time and across user environments."
        $ref: "#/definitions/CVSSv3AttackVector"
      attack_complexity:
        $ref: "#/definitions/CVSSv3AttackComplexity"
      privileges_required:
        $ref: "#/definitions/CVSSv3PrivilegesRequired"
      user_interaction:
        $ref: "#/definitions/CVSSv3UserInteraction"
      scope:
        $ref: "#/definitions/CVSSv3Scope"
      confidentiality_impact:
        $ref: "#/definitions/CVSSv3Impact"
      integrity_impact:
        $ref: "#/definitions/CVSSv3Impact"
      availability_impact:
        $ref: "#/definitions/CVSSv3Impact"
    title: "Common Vulnerability Scoring System version 3.\nFor details, see https://www.first.org/cvss/specification-document"
    example:
      attack_complexity: {}
      base_score: 1.4658129
      user_interaction: {}
      scope: {}
      impact_score: 5.637377
      confidentiality_impact: {}
      attack_vector: {}
      exploitability_score: 5.962134
      privileges_required: {}
  vulnerabilityPackageIssue:
    type: "object"
    properties:
      affected_location:
        description: "Required. The location of the vulnerability."
        $ref: "#/definitions/vulnerabilityVulnerabilityLocation"
      fixed_location:
        description: "The location of the available fix for vulnerability."
        $ref: "#/definitions/vulnerabilityVulnerabilityLocation"
      severity_name:
        type: "string"
        description: "Deprecated, use Details.effective_severity instead\nThe severity\
          \ (e.g., distro assigned severity) for this vulnerability."
    description: "This message wraps a location affected by a vulnerability and its\n\
      associated fix (if one is available)."
    example:
      fixed_location:
        package: "package"
        cpe_uri: "cpe_uri"
        version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
      affected_location:
        package: "package"
        cpe_uri: "cpe_uri"
        version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
      severity_name: "severity_name"
  vulnerabilitySeverity:
    type: "string"
    description: "Note provider-assigned severity/impact ranking.\n\n - SEVERITY_UNSPECIFIED:\
      \ Unknown.\n - MINIMAL: Minimal severity.\n - LOW: Low severity.\n - MEDIUM:\
      \ Medium severity.\n - HIGH: High severity.\n - CRITICAL: Critical severity."
    enum:
    - "SEVERITY_UNSPECIFIED"
    - "MINIMAL"
    - "LOW"
    - "MEDIUM"
    - "HIGH"
    - "CRITICAL"
    default: "SEVERITY_UNSPECIFIED"
  vulnerabilityVulnerability:
    type: "object"
    properties:
      cvss_score:
        type: "number"
        format: "float"
        description: "The CVSS score for this vulnerability."
      severity:
        description: "Note provider assigned impact of the vulnerability."
        $ref: "#/definitions/vulnerabilitySeverity"
      details:
        type: "array"
        description: "All information about the package to specifically identify this\n\
          vulnerability. One entry per (version range and cpe_uri) the package\nvulnerability\
          \ has manifested in."
        items:
          $ref: "#/definitions/VulnerabilityDetail"
      cvss_v3:
        description: "The full description of the CVSSv3."
        $ref: "#/definitions/vulnerabilityCVSSv3"
      windows_details:
        type: "array"
        description: "Windows details get their own format because the information\
          \ format and\nmodel don't match a normal detail. Specifically Windows updates\
          \ are done as\npatches, thus Windows vulnerabilities really are a missing\
          \ package, rather\nthan a package being at an incorrect version."
        items:
          $ref: "#/definitions/VulnerabilityWindowsDetail"
      source_update_time:
        type: "string"
        format: "date-time"
        description: "The time this information was last changed at the source. This\
          \ is an\nupstream timestamp from the underlying information source - e.g.\
          \ Ubuntu\nsecurity tracker."
    description: "Vulnerability provides metadata about a security vulnerability in\
      \ a Note."
    example:
      severity: {}
      cvss_score: 0.8008282
      cvss_v3:
        attack_complexity: {}
        base_score: 1.4658129
        user_interaction: {}
        scope: {}
        impact_score: 5.637377
        confidentiality_impact: {}
        attack_vector: {}
        exploitability_score: 5.962134
        privileges_required: {}
      details:
      - fixed_location:
          package: "package"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
        package: "package"
        cpe_uri: "cpe_uri"
        max_affected_version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
        description: "description"
        min_affected_version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
        severity_name: "severity_name"
        source_update_time: "2000-01-23T04:56:07.000+00:00"
        package_type: "package_type"
        is_obsolete: true
      - fixed_location:
          package: "package"
          cpe_uri: "cpe_uri"
          version:
            kind: {}
            name: "name"
            epoch: 6
            revision: "revision"
        package: "package"
        cpe_uri: "cpe_uri"
        max_affected_version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
        description: "description"
        min_affected_version:
          kind: {}
          name: "name"
          epoch: 6
          revision: "revision"
        severity_name: "severity_name"
        source_update_time: "2000-01-23T04:56:07.000+00:00"
        package_type: "package_type"
        is_obsolete: true
      source_update_time: "2000-01-23T04:56:07.000+00:00"
      windows_details:
      - cpe_uri: "cpe_uri"
        fixing_kbs:
        - name: "name"
          url: "url"
        - name: "name"
          url: "url"
        name: "name"
        description: "description"
      - cpe_uri: "cpe_uri"
        fixing_kbs:
        - name: "name"
          url: "url"
        - name: "name"
          url: "url"
        name: "name"
        description: "description"
  vulnerabilityVulnerabilityLocation:
    type: "object"
    properties:
      cpe_uri:
        type: "string"
        description: "Required. The CPE URI in [cpe format](https://cpe.mitre.org/specification/)\n\
          format. Examples include distro or storage location for vulnerable jar."
      package:
        type: "string"
        description: "Required. The package being described."
      version:
        description: "Required. The version of the package being described."
        $ref: "#/definitions/packageVersion"
    description: "The location of the vulnerability."
    example:
      package: "package"
      cpe_uri: "cpe_uri"
      version:
        kind: {}
        name: "name"
        epoch: 6
        revision: "revision"
