# V1beta1ListNotesResponse

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Notes** | [**[]V1beta1Note**](v1beta1Note.md) | The notes requested. | [optional] [default to null]
**NextPageToken** | **string** | The next pagination token in the list response. It should be used as &#x60;page_token&#x60; for the following request. An empty value means no more results. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


