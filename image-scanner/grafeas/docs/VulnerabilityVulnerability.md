# VulnerabilityVulnerability

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CvssScore** | **float32** | The CVSS score for this vulnerability. | [optional] [default to null]
**Severity** | [***VulnerabilitySeverity**](vulnerabilitySeverity.md) | Note provider assigned impact of the vulnerability. | [optional] [default to null]
**Details** | [**[]VulnerabilityDetail**](VulnerabilityDetail.md) | All information about the package to specifically identify this vulnerability. One entry per (version range and cpe_uri) the package vulnerability has manifested in. | [optional] [default to null]
**CvssV3** | [***VulnerabilityCvsSv3**](vulnerabilityCVSSv3.md) | The full description of the CVSSv3. | [optional] [default to null]
**WindowsDetails** | [**[]VulnerabilityWindowsDetail**](VulnerabilityWindowsDetail.md) | Windows details get their own format because the information format and model don&#39;t match a normal detail. Specifically Windows updates are done as patches, thus Windows vulnerabilities really are a missing package, rather than a package being at an incorrect version. | [optional] [default to null]
**SourceUpdateTime** | [**time.Time**](time.Time.md) | The time this information was last changed at the source. This is an upstream timestamp from the underlying information source - e.g. Ubuntu security tracker. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


