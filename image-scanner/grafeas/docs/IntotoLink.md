# IntotoLink

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**EffectiveCommand** | **[]string** |  | [optional] [default to null]
**Materials** | [**[]IntotoLinkArtifact**](intotoLinkArtifact.md) |  | [optional] [default to null]
**Products** | [**[]IntotoLinkArtifact**](intotoLinkArtifact.md) | Products are the supply chain artifacts generated as a result of the step. The structure is identical to that of materials. | [optional] [default to null]
**Byproducts** | [***LinkByProducts**](LinkByProducts.md) | ByProducts are data generated as part of a software supply chain step, but are not the actual result of the step. | [optional] [default to null]
**Environment** | [***LinkEnvironment**](LinkEnvironment.md) |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


