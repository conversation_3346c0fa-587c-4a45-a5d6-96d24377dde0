# V1beta1packageLocation

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CpeUri** | **string** | Required. The CPE URI in [CPE format](https://cpe.mitre.org/specification/) denoting the package manager version distributing a package. | [optional] [default to null]
**Version** | [***PackageVersion**](packageVersion.md) | The version installed at this location. | [optional] [default to null]
**Path** | **string** | The path from which we gathered that this package/version is installed. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


