# V1beta1vulnerabilityDetails

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Type_** | **string** |  | [optional] [default to null]
**Severity** | [***VulnerabilitySeverity**](vulnerabilitySeverity.md) | Output only. The note provider assigned Severity of the vulnerability. | [optional] [default to null]
**CvssScore** | **float32** | Output only. The CVSS score of this vulnerability. CVSS score is on a scale of 0-10 where 0 indicates low severity and 10 indicates high severity. | [optional] [default to null]
**PackageIssue** | [**[]VulnerabilityPackageIssue**](vulnerabilityPackageIssue.md) | Required. The set of affected locations and their fixes (if available) within the associated resource. | [optional] [default to null]
**ShortDescription** | **string** | Output only. A one sentence description of this vulnerability. | [optional] [default to null]
**LongDescription** | **string** | Output only. A detailed description of this vulnerability. | [optional] [default to null]
**RelatedUrls** | [**[]V1beta1RelatedUrl**](v1beta1RelatedUrl.md) | Output only. URLs related to this vulnerability. | [optional] [default to null]
**EffectiveSeverity** | [***VulnerabilitySeverity**](vulnerabilitySeverity.md) | The distro assigned severity for this vulnerability when it is available, and note provider assigned severity when distro has not yet assigned a severity for this vulnerability. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


