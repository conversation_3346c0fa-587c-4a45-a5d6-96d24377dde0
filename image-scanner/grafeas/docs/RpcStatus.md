# RpcStatus

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Code** | **int32** | The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code]. | [optional] [default to null]
**Message** | **string** | A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client. | [optional] [default to null]
**Details** | [**[]ProtobufAny**](protobufAny.md) | A list of messages that carry the error details.  There is a common set of message types for APIs to use. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


