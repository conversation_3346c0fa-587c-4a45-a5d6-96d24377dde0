# V1beta1ListOccurrencesResponse

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Occurrences** | [**[]V1beta1Occurrence**](v1beta1Occurrence.md) | The occurrences requested. | [optional] [default to null]
**NextPageToken** | **string** | The next pagination token in the list response. It should be used as &#x60;page_token&#x60; for the following request. An empty value means no more results. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


