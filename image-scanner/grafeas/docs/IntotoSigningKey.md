# IntotoSigningKey

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**KeyId** | **string** | key_id is an identifier for the signing key. | [optional] [default to null]
**KeyType** | **string** | This field identifies the specific signing method. Eg: \&quot;rsa\&quot;, \&quot;ed25519\&quot;, and \&quot;ecdsa\&quot;. | [optional] [default to null]
**PublicKeyValue** | **string** | This field contains the actual public key. | [optional] [default to null]
**KeyScheme** | **string** | This field contains the corresponding signature scheme. Eg: \&quot;rsassa-pss-sha256\&quot;. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


