# VulnerabilityWindowsDetail

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CpeUri** | **string** | Required. The CPE URI in [cpe format](https://cpe.mitre.org/specification/) in which the vulnerability manifests. Examples include distro or storage location for vulnerable jar. | [optional] [default to null]
**Name** | **string** | Required. The name of the vulnerability. | [optional] [default to null]
**Description** | **string** | The description of the vulnerability. | [optional] [default to null]
**FixingKbs** | [**[]WindowsDetailKnowledgeBase**](WindowsDetailKnowledgeBase.md) | Required. The names of the KBs which have hotfixes to mitigate this vulnerability. Note that there may be multiple hotfixes (and thus multiple KBs) that mitigate a given vulnerability. Currently any listed kb&#39;s presence is considered a fix. | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


