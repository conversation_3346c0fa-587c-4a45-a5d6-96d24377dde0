// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	rest "k8s.io/client-go/rest"
)

// HttpTransportInterface is an autogenerated mock type for the HttpTransportInterface type
type HttpTransportInterface struct {
	mock.Mock
}

// OverrideConfigWithCustomTransport provides a mock function with given fields: config
func (_m *HttpTransportInterface) OverrideConfigWithCustomTransport(config *rest.Config) (*rest.Config, error) {
	ret := _m.Called(config)

	if len(ret) == 0 {
		panic("no return value specified for OverrideConfigWithCustomTransport")
	}

	var r0 *rest.Config
	var r1 error
	if rf, ok := ret.Get(0).(func(*rest.Config) (*rest.Config, error)); ok {
		return rf(config)
	}
	if rf, ok := ret.Get(0).(func(*rest.Config) *rest.Config); ok {
		r0 = rf(config)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*rest.Config)
		}
	}

	if rf, ok := ret.Get(1).(func(*rest.Config) error); ok {
		r1 = rf(config)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewHttpTransportInterface creates a new instance of HttpTransportInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewHttpTransportInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *HttpTransportInterface {
	mock := &HttpTransportInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
