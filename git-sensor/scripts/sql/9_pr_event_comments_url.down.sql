/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

ALTER TABLE git_host_webhook_event_selectors DROP COLUMN IF EXISTS to_use_in_ci_env_variable;

ALTER TABLE webhook_event_parsed_data DROP COLUMN IF EXISTS ci_env_variable_data;

DELETE FROM git_host_webhook_event_selectors WHERE event_id = 1 and name = 'comments url';